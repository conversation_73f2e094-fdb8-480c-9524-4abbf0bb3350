{"comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "python", "libraryPackage": "google.cloud.aiplatform_v1beta1", "protoPackage": "google.cloud.aiplatform.v1beta1", "schema": "1.0", "services": {"DatasetService": {"clients": {"grpc": {"libraryClient": "DatasetServiceClient", "rpcs": {"AssembleData": {"methods": ["assemble_data"]}, "AssessData": {"methods": ["assess_data"]}, "CreateDataset": {"methods": ["create_dataset"]}, "CreateDatasetVersion": {"methods": ["create_dataset_version"]}, "DeleteDataset": {"methods": ["delete_dataset"]}, "DeleteDatasetVersion": {"methods": ["delete_dataset_version"]}, "DeleteSavedQuery": {"methods": ["delete_saved_query"]}, "ExportData": {"methods": ["export_data"]}, "GetAnnotationSpec": {"methods": ["get_annotation_spec"]}, "GetDataset": {"methods": ["get_dataset"]}, "GetDatasetVersion": {"methods": ["get_dataset_version"]}, "ImportData": {"methods": ["import_data"]}, "ListAnnotations": {"methods": ["list_annotations"]}, "ListDataItems": {"methods": ["list_data_items"]}, "ListDatasetVersions": {"methods": ["list_dataset_versions"]}, "ListDatasets": {"methods": ["list_datasets"]}, "ListSavedQueries": {"methods": ["list_saved_queries"]}, "RestoreDatasetVersion": {"methods": ["restore_dataset_version"]}, "SearchDataItems": {"methods": ["search_data_items"]}, "UpdateDataset": {"methods": ["update_dataset"]}, "UpdateDatasetVersion": {"methods": ["update_dataset_version"]}}}, "grpc-async": {"libraryClient": "DatasetServiceAsyncClient", "rpcs": {"AssembleData": {"methods": ["assemble_data"]}, "AssessData": {"methods": ["assess_data"]}, "CreateDataset": {"methods": ["create_dataset"]}, "CreateDatasetVersion": {"methods": ["create_dataset_version"]}, "DeleteDataset": {"methods": ["delete_dataset"]}, "DeleteDatasetVersion": {"methods": ["delete_dataset_version"]}, "DeleteSavedQuery": {"methods": ["delete_saved_query"]}, "ExportData": {"methods": ["export_data"]}, "GetAnnotationSpec": {"methods": ["get_annotation_spec"]}, "GetDataset": {"methods": ["get_dataset"]}, "GetDatasetVersion": {"methods": ["get_dataset_version"]}, "ImportData": {"methods": ["import_data"]}, "ListAnnotations": {"methods": ["list_annotations"]}, "ListDataItems": {"methods": ["list_data_items"]}, "ListDatasetVersions": {"methods": ["list_dataset_versions"]}, "ListDatasets": {"methods": ["list_datasets"]}, "ListSavedQueries": {"methods": ["list_saved_queries"]}, "RestoreDatasetVersion": {"methods": ["restore_dataset_version"]}, "SearchDataItems": {"methods": ["search_data_items"]}, "UpdateDataset": {"methods": ["update_dataset"]}, "UpdateDatasetVersion": {"methods": ["update_dataset_version"]}}}, "rest": {"libraryClient": "DatasetServiceClient", "rpcs": {"AssembleData": {"methods": ["assemble_data"]}, "AssessData": {"methods": ["assess_data"]}, "CreateDataset": {"methods": ["create_dataset"]}, "CreateDatasetVersion": {"methods": ["create_dataset_version"]}, "DeleteDataset": {"methods": ["delete_dataset"]}, "DeleteDatasetVersion": {"methods": ["delete_dataset_version"]}, "DeleteSavedQuery": {"methods": ["delete_saved_query"]}, "ExportData": {"methods": ["export_data"]}, "GetAnnotationSpec": {"methods": ["get_annotation_spec"]}, "GetDataset": {"methods": ["get_dataset"]}, "GetDatasetVersion": {"methods": ["get_dataset_version"]}, "ImportData": {"methods": ["import_data"]}, "ListAnnotations": {"methods": ["list_annotations"]}, "ListDataItems": {"methods": ["list_data_items"]}, "ListDatasetVersions": {"methods": ["list_dataset_versions"]}, "ListDatasets": {"methods": ["list_datasets"]}, "ListSavedQueries": {"methods": ["list_saved_queries"]}, "RestoreDatasetVersion": {"methods": ["restore_dataset_version"]}, "SearchDataItems": {"methods": ["search_data_items"]}, "UpdateDataset": {"methods": ["update_dataset"]}, "UpdateDatasetVersion": {"methods": ["update_dataset_version"]}}}}}, "DeploymentResourcePoolService": {"clients": {"grpc": {"libraryClient": "DeploymentResourcePoolServiceClient", "rpcs": {"CreateDeploymentResourcePool": {"methods": ["create_deployment_resource_pool"]}, "DeleteDeploymentResourcePool": {"methods": ["delete_deployment_resource_pool"]}, "GetDeploymentResourcePool": {"methods": ["get_deployment_resource_pool"]}, "ListDeploymentResourcePools": {"methods": ["list_deployment_resource_pools"]}, "QueryDeployedModels": {"methods": ["query_deployed_models"]}, "UpdateDeploymentResourcePool": {"methods": ["update_deployment_resource_pool"]}}}, "grpc-async": {"libraryClient": "DeploymentResourcePoolServiceAsyncClient", "rpcs": {"CreateDeploymentResourcePool": {"methods": ["create_deployment_resource_pool"]}, "DeleteDeploymentResourcePool": {"methods": ["delete_deployment_resource_pool"]}, "GetDeploymentResourcePool": {"methods": ["get_deployment_resource_pool"]}, "ListDeploymentResourcePools": {"methods": ["list_deployment_resource_pools"]}, "QueryDeployedModels": {"methods": ["query_deployed_models"]}, "UpdateDeploymentResourcePool": {"methods": ["update_deployment_resource_pool"]}}}, "rest": {"libraryClient": "DeploymentResourcePoolServiceClient", "rpcs": {"CreateDeploymentResourcePool": {"methods": ["create_deployment_resource_pool"]}, "DeleteDeploymentResourcePool": {"methods": ["delete_deployment_resource_pool"]}, "GetDeploymentResourcePool": {"methods": ["get_deployment_resource_pool"]}, "ListDeploymentResourcePools": {"methods": ["list_deployment_resource_pools"]}, "QueryDeployedModels": {"methods": ["query_deployed_models"]}, "UpdateDeploymentResourcePool": {"methods": ["update_deployment_resource_pool"]}}}}}, "EndpointService": {"clients": {"grpc": {"libraryClient": "EndpointServiceClient", "rpcs": {"CreateEndpoint": {"methods": ["create_endpoint"]}, "DeleteEndpoint": {"methods": ["delete_endpoint"]}, "DeployModel": {"methods": ["deploy_model"]}, "FetchPublisherModelConfig": {"methods": ["fetch_publisher_model_config"]}, "GetEndpoint": {"methods": ["get_endpoint"]}, "ListEndpoints": {"methods": ["list_endpoints"]}, "MutateDeployedModel": {"methods": ["mutate_deployed_model"]}, "SetPublisherModelConfig": {"methods": ["set_publisher_model_config"]}, "UndeployModel": {"methods": ["undeploy_model"]}, "UpdateEndpoint": {"methods": ["update_endpoint"]}, "UpdateEndpointLongRunning": {"methods": ["update_endpoint_long_running"]}}}, "grpc-async": {"libraryClient": "EndpointServiceAsyncClient", "rpcs": {"CreateEndpoint": {"methods": ["create_endpoint"]}, "DeleteEndpoint": {"methods": ["delete_endpoint"]}, "DeployModel": {"methods": ["deploy_model"]}, "FetchPublisherModelConfig": {"methods": ["fetch_publisher_model_config"]}, "GetEndpoint": {"methods": ["get_endpoint"]}, "ListEndpoints": {"methods": ["list_endpoints"]}, "MutateDeployedModel": {"methods": ["mutate_deployed_model"]}, "SetPublisherModelConfig": {"methods": ["set_publisher_model_config"]}, "UndeployModel": {"methods": ["undeploy_model"]}, "UpdateEndpoint": {"methods": ["update_endpoint"]}, "UpdateEndpointLongRunning": {"methods": ["update_endpoint_long_running"]}}}, "rest": {"libraryClient": "EndpointServiceClient", "rpcs": {"CreateEndpoint": {"methods": ["create_endpoint"]}, "DeleteEndpoint": {"methods": ["delete_endpoint"]}, "DeployModel": {"methods": ["deploy_model"]}, "FetchPublisherModelConfig": {"methods": ["fetch_publisher_model_config"]}, "GetEndpoint": {"methods": ["get_endpoint"]}, "ListEndpoints": {"methods": ["list_endpoints"]}, "MutateDeployedModel": {"methods": ["mutate_deployed_model"]}, "SetPublisherModelConfig": {"methods": ["set_publisher_model_config"]}, "UndeployModel": {"methods": ["undeploy_model"]}, "UpdateEndpoint": {"methods": ["update_endpoint"]}, "UpdateEndpointLongRunning": {"methods": ["update_endpoint_long_running"]}}}}}, "EvaluationService": {"clients": {"grpc": {"libraryClient": "EvaluationServiceClient", "rpcs": {"EvaluateDataset": {"methods": ["evaluate_dataset"]}, "EvaluateInstances": {"methods": ["evaluate_instances"]}}}, "grpc-async": {"libraryClient": "EvaluationServiceAsyncClient", "rpcs": {"EvaluateDataset": {"methods": ["evaluate_dataset"]}, "EvaluateInstances": {"methods": ["evaluate_instances"]}}}, "rest": {"libraryClient": "EvaluationServiceClient", "rpcs": {"EvaluateDataset": {"methods": ["evaluate_dataset"]}, "EvaluateInstances": {"methods": ["evaluate_instances"]}}}}}, "ExampleStoreService": {"clients": {"grpc": {"libraryClient": "ExampleStoreServiceClient", "rpcs": {"CreateExampleStore": {"methods": ["create_example_store"]}, "DeleteExampleStore": {"methods": ["delete_example_store"]}, "FetchExamples": {"methods": ["fetch_examples"]}, "GetExampleStore": {"methods": ["get_example_store"]}, "ListExampleStores": {"methods": ["list_example_stores"]}, "RemoveExamples": {"methods": ["remove_examples"]}, "SearchExamples": {"methods": ["search_examples"]}, "UpdateExampleStore": {"methods": ["update_example_store"]}, "UpsertExamples": {"methods": ["upsert_examples"]}}}, "grpc-async": {"libraryClient": "ExampleStoreServiceAsyncClient", "rpcs": {"CreateExampleStore": {"methods": ["create_example_store"]}, "DeleteExampleStore": {"methods": ["delete_example_store"]}, "FetchExamples": {"methods": ["fetch_examples"]}, "GetExampleStore": {"methods": ["get_example_store"]}, "ListExampleStores": {"methods": ["list_example_stores"]}, "RemoveExamples": {"methods": ["remove_examples"]}, "SearchExamples": {"methods": ["search_examples"]}, "UpdateExampleStore": {"methods": ["update_example_store"]}, "UpsertExamples": {"methods": ["upsert_examples"]}}}, "rest": {"libraryClient": "ExampleStoreServiceClient", "rpcs": {"CreateExampleStore": {"methods": ["create_example_store"]}, "DeleteExampleStore": {"methods": ["delete_example_store"]}, "FetchExamples": {"methods": ["fetch_examples"]}, "GetExampleStore": {"methods": ["get_example_store"]}, "ListExampleStores": {"methods": ["list_example_stores"]}, "RemoveExamples": {"methods": ["remove_examples"]}, "SearchExamples": {"methods": ["search_examples"]}, "UpdateExampleStore": {"methods": ["update_example_store"]}, "UpsertExamples": {"methods": ["upsert_examples"]}}}}}, "ExtensionExecutionService": {"clients": {"grpc": {"libraryClient": "ExtensionExecutionServiceClient", "rpcs": {"ExecuteExtension": {"methods": ["execute_extension"]}, "QueryExtension": {"methods": ["query_extension"]}}}, "grpc-async": {"libraryClient": "ExtensionExecutionServiceAsyncClient", "rpcs": {"ExecuteExtension": {"methods": ["execute_extension"]}, "QueryExtension": {"methods": ["query_extension"]}}}, "rest": {"libraryClient": "ExtensionExecutionServiceClient", "rpcs": {"ExecuteExtension": {"methods": ["execute_extension"]}, "QueryExtension": {"methods": ["query_extension"]}}}}}, "ExtensionRegistryService": {"clients": {"grpc": {"libraryClient": "ExtensionRegistryServiceClient", "rpcs": {"DeleteExtension": {"methods": ["delete_extension"]}, "GetExtension": {"methods": ["get_extension"]}, "ImportExtension": {"methods": ["import_extension"]}, "ListExtensions": {"methods": ["list_extensions"]}, "UpdateExtension": {"methods": ["update_extension"]}}}, "grpc-async": {"libraryClient": "ExtensionRegistryServiceAsyncClient", "rpcs": {"DeleteExtension": {"methods": ["delete_extension"]}, "GetExtension": {"methods": ["get_extension"]}, "ImportExtension": {"methods": ["import_extension"]}, "ListExtensions": {"methods": ["list_extensions"]}, "UpdateExtension": {"methods": ["update_extension"]}}}, "rest": {"libraryClient": "ExtensionRegistryServiceClient", "rpcs": {"DeleteExtension": {"methods": ["delete_extension"]}, "GetExtension": {"methods": ["get_extension"]}, "ImportExtension": {"methods": ["import_extension"]}, "ListExtensions": {"methods": ["list_extensions"]}, "UpdateExtension": {"methods": ["update_extension"]}}}}}, "FeatureOnlineStoreAdminService": {"clients": {"grpc": {"libraryClient": "FeatureOnlineStoreAdminServiceClient", "rpcs": {"CreateFeatureOnlineStore": {"methods": ["create_feature_online_store"]}, "CreateFeatureView": {"methods": ["create_feature_view"]}, "DeleteFeatureOnlineStore": {"methods": ["delete_feature_online_store"]}, "DeleteFeatureView": {"methods": ["delete_feature_view"]}, "GetFeatureOnlineStore": {"methods": ["get_feature_online_store"]}, "GetFeatureView": {"methods": ["get_feature_view"]}, "GetFeatureViewSync": {"methods": ["get_feature_view_sync"]}, "ListFeatureOnlineStores": {"methods": ["list_feature_online_stores"]}, "ListFeatureViewSyncs": {"methods": ["list_feature_view_syncs"]}, "ListFeatureViews": {"methods": ["list_feature_views"]}, "SyncFeatureView": {"methods": ["sync_feature_view"]}, "UpdateFeatureOnlineStore": {"methods": ["update_feature_online_store"]}, "UpdateFeatureView": {"methods": ["update_feature_view"]}}}, "grpc-async": {"libraryClient": "FeatureOnlineStoreAdminServiceAsyncClient", "rpcs": {"CreateFeatureOnlineStore": {"methods": ["create_feature_online_store"]}, "CreateFeatureView": {"methods": ["create_feature_view"]}, "DeleteFeatureOnlineStore": {"methods": ["delete_feature_online_store"]}, "DeleteFeatureView": {"methods": ["delete_feature_view"]}, "GetFeatureOnlineStore": {"methods": ["get_feature_online_store"]}, "GetFeatureView": {"methods": ["get_feature_view"]}, "GetFeatureViewSync": {"methods": ["get_feature_view_sync"]}, "ListFeatureOnlineStores": {"methods": ["list_feature_online_stores"]}, "ListFeatureViewSyncs": {"methods": ["list_feature_view_syncs"]}, "ListFeatureViews": {"methods": ["list_feature_views"]}, "SyncFeatureView": {"methods": ["sync_feature_view"]}, "UpdateFeatureOnlineStore": {"methods": ["update_feature_online_store"]}, "UpdateFeatureView": {"methods": ["update_feature_view"]}}}, "rest": {"libraryClient": "FeatureOnlineStoreAdminServiceClient", "rpcs": {"CreateFeatureOnlineStore": {"methods": ["create_feature_online_store"]}, "CreateFeatureView": {"methods": ["create_feature_view"]}, "DeleteFeatureOnlineStore": {"methods": ["delete_feature_online_store"]}, "DeleteFeatureView": {"methods": ["delete_feature_view"]}, "GetFeatureOnlineStore": {"methods": ["get_feature_online_store"]}, "GetFeatureView": {"methods": ["get_feature_view"]}, "GetFeatureViewSync": {"methods": ["get_feature_view_sync"]}, "ListFeatureOnlineStores": {"methods": ["list_feature_online_stores"]}, "ListFeatureViewSyncs": {"methods": ["list_feature_view_syncs"]}, "ListFeatureViews": {"methods": ["list_feature_views"]}, "SyncFeatureView": {"methods": ["sync_feature_view"]}, "UpdateFeatureOnlineStore": {"methods": ["update_feature_online_store"]}, "UpdateFeatureView": {"methods": ["update_feature_view"]}}}}}, "FeatureOnlineStoreService": {"clients": {"grpc": {"libraryClient": "FeatureOnlineStoreServiceClient", "rpcs": {"FeatureViewDirectWrite": {"methods": ["feature_view_direct_write"]}, "FetchFeatureValues": {"methods": ["fetch_feature_values"]}, "SearchNearestEntities": {"methods": ["search_nearest_entities"]}, "StreamingFetchFeatureValues": {"methods": ["streaming_fetch_feature_values"]}}}, "grpc-async": {"libraryClient": "FeatureOnlineStoreServiceAsyncClient", "rpcs": {"FeatureViewDirectWrite": {"methods": ["feature_view_direct_write"]}, "FetchFeatureValues": {"methods": ["fetch_feature_values"]}, "SearchNearestEntities": {"methods": ["search_nearest_entities"]}, "StreamingFetchFeatureValues": {"methods": ["streaming_fetch_feature_values"]}}}, "rest": {"libraryClient": "FeatureOnlineStoreServiceClient", "rpcs": {"FeatureViewDirectWrite": {"methods": ["feature_view_direct_write"]}, "FetchFeatureValues": {"methods": ["fetch_feature_values"]}, "SearchNearestEntities": {"methods": ["search_nearest_entities"]}, "StreamingFetchFeatureValues": {"methods": ["streaming_fetch_feature_values"]}}}}}, "FeatureRegistryService": {"clients": {"grpc": {"libraryClient": "FeatureRegistryServiceClient", "rpcs": {"BatchCreateFeatures": {"methods": ["batch_create_features"]}, "CreateFeature": {"methods": ["create_feature"]}, "CreateFeatureGroup": {"methods": ["create_feature_group"]}, "CreateFeatureMonitor": {"methods": ["create_feature_monitor"]}, "CreateFeatureMonitorJob": {"methods": ["create_feature_monitor_job"]}, "DeleteFeature": {"methods": ["delete_feature"]}, "DeleteFeatureGroup": {"methods": ["delete_feature_group"]}, "DeleteFeatureMonitor": {"methods": ["delete_feature_monitor"]}, "GetFeature": {"methods": ["get_feature"]}, "GetFeatureGroup": {"methods": ["get_feature_group"]}, "GetFeatureMonitor": {"methods": ["get_feature_monitor"]}, "GetFeatureMonitorJob": {"methods": ["get_feature_monitor_job"]}, "ListFeatureGroups": {"methods": ["list_feature_groups"]}, "ListFeatureMonitorJobs": {"methods": ["list_feature_monitor_jobs"]}, "ListFeatureMonitors": {"methods": ["list_feature_monitors"]}, "ListFeatures": {"methods": ["list_features"]}, "UpdateFeature": {"methods": ["update_feature"]}, "UpdateFeatureGroup": {"methods": ["update_feature_group"]}, "UpdateFeatureMonitor": {"methods": ["update_feature_monitor"]}}}, "grpc-async": {"libraryClient": "FeatureRegistryServiceAsyncClient", "rpcs": {"BatchCreateFeatures": {"methods": ["batch_create_features"]}, "CreateFeature": {"methods": ["create_feature"]}, "CreateFeatureGroup": {"methods": ["create_feature_group"]}, "CreateFeatureMonitor": {"methods": ["create_feature_monitor"]}, "CreateFeatureMonitorJob": {"methods": ["create_feature_monitor_job"]}, "DeleteFeature": {"methods": ["delete_feature"]}, "DeleteFeatureGroup": {"methods": ["delete_feature_group"]}, "DeleteFeatureMonitor": {"methods": ["delete_feature_monitor"]}, "GetFeature": {"methods": ["get_feature"]}, "GetFeatureGroup": {"methods": ["get_feature_group"]}, "GetFeatureMonitor": {"methods": ["get_feature_monitor"]}, "GetFeatureMonitorJob": {"methods": ["get_feature_monitor_job"]}, "ListFeatureGroups": {"methods": ["list_feature_groups"]}, "ListFeatureMonitorJobs": {"methods": ["list_feature_monitor_jobs"]}, "ListFeatureMonitors": {"methods": ["list_feature_monitors"]}, "ListFeatures": {"methods": ["list_features"]}, "UpdateFeature": {"methods": ["update_feature"]}, "UpdateFeatureGroup": {"methods": ["update_feature_group"]}, "UpdateFeatureMonitor": {"methods": ["update_feature_monitor"]}}}, "rest": {"libraryClient": "FeatureRegistryServiceClient", "rpcs": {"BatchCreateFeatures": {"methods": ["batch_create_features"]}, "CreateFeature": {"methods": ["create_feature"]}, "CreateFeatureGroup": {"methods": ["create_feature_group"]}, "CreateFeatureMonitor": {"methods": ["create_feature_monitor"]}, "CreateFeatureMonitorJob": {"methods": ["create_feature_monitor_job"]}, "DeleteFeature": {"methods": ["delete_feature"]}, "DeleteFeatureGroup": {"methods": ["delete_feature_group"]}, "DeleteFeatureMonitor": {"methods": ["delete_feature_monitor"]}, "GetFeature": {"methods": ["get_feature"]}, "GetFeatureGroup": {"methods": ["get_feature_group"]}, "GetFeatureMonitor": {"methods": ["get_feature_monitor"]}, "GetFeatureMonitorJob": {"methods": ["get_feature_monitor_job"]}, "ListFeatureGroups": {"methods": ["list_feature_groups"]}, "ListFeatureMonitorJobs": {"methods": ["list_feature_monitor_jobs"]}, "ListFeatureMonitors": {"methods": ["list_feature_monitors"]}, "ListFeatures": {"methods": ["list_features"]}, "UpdateFeature": {"methods": ["update_feature"]}, "UpdateFeatureGroup": {"methods": ["update_feature_group"]}, "UpdateFeatureMonitor": {"methods": ["update_feature_monitor"]}}}}}, "FeaturestoreOnlineServingService": {"clients": {"grpc": {"libraryClient": "FeaturestoreOnlineServingServiceClient", "rpcs": {"ReadFeatureValues": {"methods": ["read_feature_values"]}, "StreamingReadFeatureValues": {"methods": ["streaming_read_feature_values"]}, "WriteFeatureValues": {"methods": ["write_feature_values"]}}}, "grpc-async": {"libraryClient": "FeaturestoreOnlineServingServiceAsyncClient", "rpcs": {"ReadFeatureValues": {"methods": ["read_feature_values"]}, "StreamingReadFeatureValues": {"methods": ["streaming_read_feature_values"]}, "WriteFeatureValues": {"methods": ["write_feature_values"]}}}, "rest": {"libraryClient": "FeaturestoreOnlineServingServiceClient", "rpcs": {"ReadFeatureValues": {"methods": ["read_feature_values"]}, "StreamingReadFeatureValues": {"methods": ["streaming_read_feature_values"]}, "WriteFeatureValues": {"methods": ["write_feature_values"]}}}}}, "FeaturestoreService": {"clients": {"grpc": {"libraryClient": "FeaturestoreServiceClient", "rpcs": {"BatchCreateFeatures": {"methods": ["batch_create_features"]}, "BatchReadFeatureValues": {"methods": ["batch_read_feature_values"]}, "CreateEntityType": {"methods": ["create_entity_type"]}, "CreateFeature": {"methods": ["create_feature"]}, "CreateFeaturestore": {"methods": ["create_featurestore"]}, "DeleteEntityType": {"methods": ["delete_entity_type"]}, "DeleteFeature": {"methods": ["delete_feature"]}, "DeleteFeatureValues": {"methods": ["delete_feature_values"]}, "DeleteFeaturestore": {"methods": ["delete_featurestore"]}, "ExportFeatureValues": {"methods": ["export_feature_values"]}, "GetEntityType": {"methods": ["get_entity_type"]}, "GetFeature": {"methods": ["get_feature"]}, "GetFeaturestore": {"methods": ["get_featurestore"]}, "ImportFeatureValues": {"methods": ["import_feature_values"]}, "ListEntityTypes": {"methods": ["list_entity_types"]}, "ListFeatures": {"methods": ["list_features"]}, "ListFeaturestores": {"methods": ["list_featurestores"]}, "SearchFeatures": {"methods": ["search_features"]}, "UpdateEntityType": {"methods": ["update_entity_type"]}, "UpdateFeature": {"methods": ["update_feature"]}, "UpdateFeaturestore": {"methods": ["update_featurestore"]}}}, "grpc-async": {"libraryClient": "FeaturestoreServiceAsyncClient", "rpcs": {"BatchCreateFeatures": {"methods": ["batch_create_features"]}, "BatchReadFeatureValues": {"methods": ["batch_read_feature_values"]}, "CreateEntityType": {"methods": ["create_entity_type"]}, "CreateFeature": {"methods": ["create_feature"]}, "CreateFeaturestore": {"methods": ["create_featurestore"]}, "DeleteEntityType": {"methods": ["delete_entity_type"]}, "DeleteFeature": {"methods": ["delete_feature"]}, "DeleteFeatureValues": {"methods": ["delete_feature_values"]}, "DeleteFeaturestore": {"methods": ["delete_featurestore"]}, "ExportFeatureValues": {"methods": ["export_feature_values"]}, "GetEntityType": {"methods": ["get_entity_type"]}, "GetFeature": {"methods": ["get_feature"]}, "GetFeaturestore": {"methods": ["get_featurestore"]}, "ImportFeatureValues": {"methods": ["import_feature_values"]}, "ListEntityTypes": {"methods": ["list_entity_types"]}, "ListFeatures": {"methods": ["list_features"]}, "ListFeaturestores": {"methods": ["list_featurestores"]}, "SearchFeatures": {"methods": ["search_features"]}, "UpdateEntityType": {"methods": ["update_entity_type"]}, "UpdateFeature": {"methods": ["update_feature"]}, "UpdateFeaturestore": {"methods": ["update_featurestore"]}}}, "rest": {"libraryClient": "FeaturestoreServiceClient", "rpcs": {"BatchCreateFeatures": {"methods": ["batch_create_features"]}, "BatchReadFeatureValues": {"methods": ["batch_read_feature_values"]}, "CreateEntityType": {"methods": ["create_entity_type"]}, "CreateFeature": {"methods": ["create_feature"]}, "CreateFeaturestore": {"methods": ["create_featurestore"]}, "DeleteEntityType": {"methods": ["delete_entity_type"]}, "DeleteFeature": {"methods": ["delete_feature"]}, "DeleteFeatureValues": {"methods": ["delete_feature_values"]}, "DeleteFeaturestore": {"methods": ["delete_featurestore"]}, "ExportFeatureValues": {"methods": ["export_feature_values"]}, "GetEntityType": {"methods": ["get_entity_type"]}, "GetFeature": {"methods": ["get_feature"]}, "GetFeaturestore": {"methods": ["get_featurestore"]}, "ImportFeatureValues": {"methods": ["import_feature_values"]}, "ListEntityTypes": {"methods": ["list_entity_types"]}, "ListFeatures": {"methods": ["list_features"]}, "ListFeaturestores": {"methods": ["list_featurestores"]}, "SearchFeatures": {"methods": ["search_features"]}, "UpdateEntityType": {"methods": ["update_entity_type"]}, "UpdateFeature": {"methods": ["update_feature"]}, "UpdateFeaturestore": {"methods": ["update_featurestore"]}}}}}, "GenAiCacheService": {"clients": {"grpc": {"libraryClient": "GenAiCacheServiceClient", "rpcs": {"CreateCachedContent": {"methods": ["create_cached_content"]}, "DeleteCachedContent": {"methods": ["delete_cached_content"]}, "GetCachedContent": {"methods": ["get_cached_content"]}, "ListCachedContents": {"methods": ["list_cached_contents"]}, "UpdateCachedContent": {"methods": ["update_cached_content"]}}}, "grpc-async": {"libraryClient": "GenAiCacheServiceAsyncClient", "rpcs": {"CreateCachedContent": {"methods": ["create_cached_content"]}, "DeleteCachedContent": {"methods": ["delete_cached_content"]}, "GetCachedContent": {"methods": ["get_cached_content"]}, "ListCachedContents": {"methods": ["list_cached_contents"]}, "UpdateCachedContent": {"methods": ["update_cached_content"]}}}, "rest": {"libraryClient": "GenAiCacheServiceClient", "rpcs": {"CreateCachedContent": {"methods": ["create_cached_content"]}, "DeleteCachedContent": {"methods": ["delete_cached_content"]}, "GetCachedContent": {"methods": ["get_cached_content"]}, "ListCachedContents": {"methods": ["list_cached_contents"]}, "UpdateCachedContent": {"methods": ["update_cached_content"]}}}}}, "GenAiTuningService": {"clients": {"grpc": {"libraryClient": "GenAiTuningServiceClient", "rpcs": {"CancelTuningJob": {"methods": ["cancel_tuning_job"]}, "CreateTuningJob": {"methods": ["create_tuning_job"]}, "GetTuningJob": {"methods": ["get_tuning_job"]}, "ListTuningJobs": {"methods": ["list_tuning_jobs"]}, "RebaseTunedModel": {"methods": ["rebase_tuned_model"]}}}, "grpc-async": {"libraryClient": "GenAiTuningServiceAsyncClient", "rpcs": {"CancelTuningJob": {"methods": ["cancel_tuning_job"]}, "CreateTuningJob": {"methods": ["create_tuning_job"]}, "GetTuningJob": {"methods": ["get_tuning_job"]}, "ListTuningJobs": {"methods": ["list_tuning_jobs"]}, "RebaseTunedModel": {"methods": ["rebase_tuned_model"]}}}, "rest": {"libraryClient": "GenAiTuningServiceClient", "rpcs": {"CancelTuningJob": {"methods": ["cancel_tuning_job"]}, "CreateTuningJob": {"methods": ["create_tuning_job"]}, "GetTuningJob": {"methods": ["get_tuning_job"]}, "ListTuningJobs": {"methods": ["list_tuning_jobs"]}, "RebaseTunedModel": {"methods": ["rebase_tuned_model"]}}}}}, "IndexEndpointService": {"clients": {"grpc": {"libraryClient": "IndexEndpointServiceClient", "rpcs": {"CreateIndexEndpoint": {"methods": ["create_index_endpoint"]}, "DeleteIndexEndpoint": {"methods": ["delete_index_endpoint"]}, "DeployIndex": {"methods": ["deploy_index"]}, "GetIndexEndpoint": {"methods": ["get_index_endpoint"]}, "ListIndexEndpoints": {"methods": ["list_index_endpoints"]}, "MutateDeployedIndex": {"methods": ["mutate_deployed_index"]}, "UndeployIndex": {"methods": ["undeploy_index"]}, "UpdateIndexEndpoint": {"methods": ["update_index_endpoint"]}}}, "grpc-async": {"libraryClient": "IndexEndpointServiceAsyncClient", "rpcs": {"CreateIndexEndpoint": {"methods": ["create_index_endpoint"]}, "DeleteIndexEndpoint": {"methods": ["delete_index_endpoint"]}, "DeployIndex": {"methods": ["deploy_index"]}, "GetIndexEndpoint": {"methods": ["get_index_endpoint"]}, "ListIndexEndpoints": {"methods": ["list_index_endpoints"]}, "MutateDeployedIndex": {"methods": ["mutate_deployed_index"]}, "UndeployIndex": {"methods": ["undeploy_index"]}, "UpdateIndexEndpoint": {"methods": ["update_index_endpoint"]}}}, "rest": {"libraryClient": "IndexEndpointServiceClient", "rpcs": {"CreateIndexEndpoint": {"methods": ["create_index_endpoint"]}, "DeleteIndexEndpoint": {"methods": ["delete_index_endpoint"]}, "DeployIndex": {"methods": ["deploy_index"]}, "GetIndexEndpoint": {"methods": ["get_index_endpoint"]}, "ListIndexEndpoints": {"methods": ["list_index_endpoints"]}, "MutateDeployedIndex": {"methods": ["mutate_deployed_index"]}, "UndeployIndex": {"methods": ["undeploy_index"]}, "UpdateIndexEndpoint": {"methods": ["update_index_endpoint"]}}}}}, "IndexService": {"clients": {"grpc": {"libraryClient": "IndexServiceClient", "rpcs": {"CreateIndex": {"methods": ["create_index"]}, "DeleteIndex": {"methods": ["delete_index"]}, "GetIndex": {"methods": ["get_index"]}, "ImportIndex": {"methods": ["import_index"]}, "ListIndexes": {"methods": ["list_indexes"]}, "RemoveDatapoints": {"methods": ["remove_datapoints"]}, "UpdateIndex": {"methods": ["update_index"]}, "UpsertDatapoints": {"methods": ["upsert_datapoints"]}}}, "grpc-async": {"libraryClient": "IndexServiceAsyncClient", "rpcs": {"CreateIndex": {"methods": ["create_index"]}, "DeleteIndex": {"methods": ["delete_index"]}, "GetIndex": {"methods": ["get_index"]}, "ImportIndex": {"methods": ["import_index"]}, "ListIndexes": {"methods": ["list_indexes"]}, "RemoveDatapoints": {"methods": ["remove_datapoints"]}, "UpdateIndex": {"methods": ["update_index"]}, "UpsertDatapoints": {"methods": ["upsert_datapoints"]}}}, "rest": {"libraryClient": "IndexServiceClient", "rpcs": {"CreateIndex": {"methods": ["create_index"]}, "DeleteIndex": {"methods": ["delete_index"]}, "GetIndex": {"methods": ["get_index"]}, "ImportIndex": {"methods": ["import_index"]}, "ListIndexes": {"methods": ["list_indexes"]}, "RemoveDatapoints": {"methods": ["remove_datapoints"]}, "UpdateIndex": {"methods": ["update_index"]}, "UpsertDatapoints": {"methods": ["upsert_datapoints"]}}}}}, "JobService": {"clients": {"grpc": {"libraryClient": "JobServiceClient", "rpcs": {"CancelBatchPredictionJob": {"methods": ["cancel_batch_prediction_job"]}, "CancelCustomJob": {"methods": ["cancel_custom_job"]}, "CancelDataLabelingJob": {"methods": ["cancel_data_labeling_job"]}, "CancelHyperparameterTuningJob": {"methods": ["cancel_hyperparameter_tuning_job"]}, "CancelNasJob": {"methods": ["cancel_nas_job"]}, "CreateBatchPredictionJob": {"methods": ["create_batch_prediction_job"]}, "CreateCustomJob": {"methods": ["create_custom_job"]}, "CreateDataLabelingJob": {"methods": ["create_data_labeling_job"]}, "CreateHyperparameterTuningJob": {"methods": ["create_hyperparameter_tuning_job"]}, "CreateModelDeploymentMonitoringJob": {"methods": ["create_model_deployment_monitoring_job"]}, "CreateNasJob": {"methods": ["create_nas_job"]}, "DeleteBatchPredictionJob": {"methods": ["delete_batch_prediction_job"]}, "DeleteCustomJob": {"methods": ["delete_custom_job"]}, "DeleteDataLabelingJob": {"methods": ["delete_data_labeling_job"]}, "DeleteHyperparameterTuningJob": {"methods": ["delete_hyperparameter_tuning_job"]}, "DeleteModelDeploymentMonitoringJob": {"methods": ["delete_model_deployment_monitoring_job"]}, "DeleteNasJob": {"methods": ["delete_nas_job"]}, "GetBatchPredictionJob": {"methods": ["get_batch_prediction_job"]}, "GetCustomJob": {"methods": ["get_custom_job"]}, "GetDataLabelingJob": {"methods": ["get_data_labeling_job"]}, "GetHyperparameterTuningJob": {"methods": ["get_hyperparameter_tuning_job"]}, "GetModelDeploymentMonitoringJob": {"methods": ["get_model_deployment_monitoring_job"]}, "GetNasJob": {"methods": ["get_nas_job"]}, "GetNasTrialDetail": {"methods": ["get_nas_trial_detail"]}, "ListBatchPredictionJobs": {"methods": ["list_batch_prediction_jobs"]}, "ListCustomJobs": {"methods": ["list_custom_jobs"]}, "ListDataLabelingJobs": {"methods": ["list_data_labeling_jobs"]}, "ListHyperparameterTuningJobs": {"methods": ["list_hyperparameter_tuning_jobs"]}, "ListModelDeploymentMonitoringJobs": {"methods": ["list_model_deployment_monitoring_jobs"]}, "ListNasJobs": {"methods": ["list_nas_jobs"]}, "ListNasTrialDetails": {"methods": ["list_nas_trial_details"]}, "PauseModelDeploymentMonitoringJob": {"methods": ["pause_model_deployment_monitoring_job"]}, "ResumeModelDeploymentMonitoringJob": {"methods": ["resume_model_deployment_monitoring_job"]}, "SearchModelDeploymentMonitoringStatsAnomalies": {"methods": ["search_model_deployment_monitoring_stats_anomalies"]}, "UpdateModelDeploymentMonitoringJob": {"methods": ["update_model_deployment_monitoring_job"]}}}, "grpc-async": {"libraryClient": "JobServiceAsyncClient", "rpcs": {"CancelBatchPredictionJob": {"methods": ["cancel_batch_prediction_job"]}, "CancelCustomJob": {"methods": ["cancel_custom_job"]}, "CancelDataLabelingJob": {"methods": ["cancel_data_labeling_job"]}, "CancelHyperparameterTuningJob": {"methods": ["cancel_hyperparameter_tuning_job"]}, "CancelNasJob": {"methods": ["cancel_nas_job"]}, "CreateBatchPredictionJob": {"methods": ["create_batch_prediction_job"]}, "CreateCustomJob": {"methods": ["create_custom_job"]}, "CreateDataLabelingJob": {"methods": ["create_data_labeling_job"]}, "CreateHyperparameterTuningJob": {"methods": ["create_hyperparameter_tuning_job"]}, "CreateModelDeploymentMonitoringJob": {"methods": ["create_model_deployment_monitoring_job"]}, "CreateNasJob": {"methods": ["create_nas_job"]}, "DeleteBatchPredictionJob": {"methods": ["delete_batch_prediction_job"]}, "DeleteCustomJob": {"methods": ["delete_custom_job"]}, "DeleteDataLabelingJob": {"methods": ["delete_data_labeling_job"]}, "DeleteHyperparameterTuningJob": {"methods": ["delete_hyperparameter_tuning_job"]}, "DeleteModelDeploymentMonitoringJob": {"methods": ["delete_model_deployment_monitoring_job"]}, "DeleteNasJob": {"methods": ["delete_nas_job"]}, "GetBatchPredictionJob": {"methods": ["get_batch_prediction_job"]}, "GetCustomJob": {"methods": ["get_custom_job"]}, "GetDataLabelingJob": {"methods": ["get_data_labeling_job"]}, "GetHyperparameterTuningJob": {"methods": ["get_hyperparameter_tuning_job"]}, "GetModelDeploymentMonitoringJob": {"methods": ["get_model_deployment_monitoring_job"]}, "GetNasJob": {"methods": ["get_nas_job"]}, "GetNasTrialDetail": {"methods": ["get_nas_trial_detail"]}, "ListBatchPredictionJobs": {"methods": ["list_batch_prediction_jobs"]}, "ListCustomJobs": {"methods": ["list_custom_jobs"]}, "ListDataLabelingJobs": {"methods": ["list_data_labeling_jobs"]}, "ListHyperparameterTuningJobs": {"methods": ["list_hyperparameter_tuning_jobs"]}, "ListModelDeploymentMonitoringJobs": {"methods": ["list_model_deployment_monitoring_jobs"]}, "ListNasJobs": {"methods": ["list_nas_jobs"]}, "ListNasTrialDetails": {"methods": ["list_nas_trial_details"]}, "PauseModelDeploymentMonitoringJob": {"methods": ["pause_model_deployment_monitoring_job"]}, "ResumeModelDeploymentMonitoringJob": {"methods": ["resume_model_deployment_monitoring_job"]}, "SearchModelDeploymentMonitoringStatsAnomalies": {"methods": ["search_model_deployment_monitoring_stats_anomalies"]}, "UpdateModelDeploymentMonitoringJob": {"methods": ["update_model_deployment_monitoring_job"]}}}, "rest": {"libraryClient": "JobServiceClient", "rpcs": {"CancelBatchPredictionJob": {"methods": ["cancel_batch_prediction_job"]}, "CancelCustomJob": {"methods": ["cancel_custom_job"]}, "CancelDataLabelingJob": {"methods": ["cancel_data_labeling_job"]}, "CancelHyperparameterTuningJob": {"methods": ["cancel_hyperparameter_tuning_job"]}, "CancelNasJob": {"methods": ["cancel_nas_job"]}, "CreateBatchPredictionJob": {"methods": ["create_batch_prediction_job"]}, "CreateCustomJob": {"methods": ["create_custom_job"]}, "CreateDataLabelingJob": {"methods": ["create_data_labeling_job"]}, "CreateHyperparameterTuningJob": {"methods": ["create_hyperparameter_tuning_job"]}, "CreateModelDeploymentMonitoringJob": {"methods": ["create_model_deployment_monitoring_job"]}, "CreateNasJob": {"methods": ["create_nas_job"]}, "DeleteBatchPredictionJob": {"methods": ["delete_batch_prediction_job"]}, "DeleteCustomJob": {"methods": ["delete_custom_job"]}, "DeleteDataLabelingJob": {"methods": ["delete_data_labeling_job"]}, "DeleteHyperparameterTuningJob": {"methods": ["delete_hyperparameter_tuning_job"]}, "DeleteModelDeploymentMonitoringJob": {"methods": ["delete_model_deployment_monitoring_job"]}, "DeleteNasJob": {"methods": ["delete_nas_job"]}, "GetBatchPredictionJob": {"methods": ["get_batch_prediction_job"]}, "GetCustomJob": {"methods": ["get_custom_job"]}, "GetDataLabelingJob": {"methods": ["get_data_labeling_job"]}, "GetHyperparameterTuningJob": {"methods": ["get_hyperparameter_tuning_job"]}, "GetModelDeploymentMonitoringJob": {"methods": ["get_model_deployment_monitoring_job"]}, "GetNasJob": {"methods": ["get_nas_job"]}, "GetNasTrialDetail": {"methods": ["get_nas_trial_detail"]}, "ListBatchPredictionJobs": {"methods": ["list_batch_prediction_jobs"]}, "ListCustomJobs": {"methods": ["list_custom_jobs"]}, "ListDataLabelingJobs": {"methods": ["list_data_labeling_jobs"]}, "ListHyperparameterTuningJobs": {"methods": ["list_hyperparameter_tuning_jobs"]}, "ListModelDeploymentMonitoringJobs": {"methods": ["list_model_deployment_monitoring_jobs"]}, "ListNasJobs": {"methods": ["list_nas_jobs"]}, "ListNasTrialDetails": {"methods": ["list_nas_trial_details"]}, "PauseModelDeploymentMonitoringJob": {"methods": ["pause_model_deployment_monitoring_job"]}, "ResumeModelDeploymentMonitoringJob": {"methods": ["resume_model_deployment_monitoring_job"]}, "SearchModelDeploymentMonitoringStatsAnomalies": {"methods": ["search_model_deployment_monitoring_stats_anomalies"]}, "UpdateModelDeploymentMonitoringJob": {"methods": ["update_model_deployment_monitoring_job"]}}}}}, "LlmUtilityService": {"clients": {"grpc": {"libraryClient": "LlmUtilityServiceClient", "rpcs": {"ComputeTokens": {"methods": ["compute_tokens"]}}}, "grpc-async": {"libraryClient": "LlmUtilityServiceAsyncClient", "rpcs": {"ComputeTokens": {"methods": ["compute_tokens"]}}}, "rest": {"libraryClient": "LlmUtilityServiceClient", "rpcs": {"ComputeTokens": {"methods": ["compute_tokens"]}}}}}, "MatchService": {"clients": {"grpc": {"libraryClient": "MatchServiceClient", "rpcs": {"FindNeighbors": {"methods": ["find_neighbors"]}, "ReadIndexDatapoints": {"methods": ["read_index_datapoints"]}}}, "grpc-async": {"libraryClient": "MatchServiceAsyncClient", "rpcs": {"FindNeighbors": {"methods": ["find_neighbors"]}, "ReadIndexDatapoints": {"methods": ["read_index_datapoints"]}}}, "rest": {"libraryClient": "MatchServiceClient", "rpcs": {"FindNeighbors": {"methods": ["find_neighbors"]}, "ReadIndexDatapoints": {"methods": ["read_index_datapoints"]}}}}}, "MetadataService": {"clients": {"grpc": {"libraryClient": "MetadataServiceClient", "rpcs": {"AddContextArtifactsAndExecutions": {"methods": ["add_context_artifacts_and_executions"]}, "AddContextChildren": {"methods": ["add_context_children"]}, "AddExecutionEvents": {"methods": ["add_execution_events"]}, "CreateArtifact": {"methods": ["create_artifact"]}, "CreateContext": {"methods": ["create_context"]}, "CreateExecution": {"methods": ["create_execution"]}, "CreateMetadataSchema": {"methods": ["create_metadata_schema"]}, "CreateMetadataStore": {"methods": ["create_metadata_store"]}, "DeleteArtifact": {"methods": ["delete_artifact"]}, "DeleteContext": {"methods": ["delete_context"]}, "DeleteExecution": {"methods": ["delete_execution"]}, "DeleteMetadataStore": {"methods": ["delete_metadata_store"]}, "GetArtifact": {"methods": ["get_artifact"]}, "GetContext": {"methods": ["get_context"]}, "GetExecution": {"methods": ["get_execution"]}, "GetMetadataSchema": {"methods": ["get_metadata_schema"]}, "GetMetadataStore": {"methods": ["get_metadata_store"]}, "ListArtifacts": {"methods": ["list_artifacts"]}, "ListContexts": {"methods": ["list_contexts"]}, "ListExecutions": {"methods": ["list_executions"]}, "ListMetadataSchemas": {"methods": ["list_metadata_schemas"]}, "ListMetadataStores": {"methods": ["list_metadata_stores"]}, "PurgeArtifacts": {"methods": ["purge_artifacts"]}, "PurgeContexts": {"methods": ["purge_contexts"]}, "PurgeExecutions": {"methods": ["purge_executions"]}, "QueryArtifactLineageSubgraph": {"methods": ["query_artifact_lineage_subgraph"]}, "QueryContextLineageSubgraph": {"methods": ["query_context_lineage_subgraph"]}, "QueryExecutionInputsAndOutputs": {"methods": ["query_execution_inputs_and_outputs"]}, "RemoveContextChildren": {"methods": ["remove_context_children"]}, "UpdateArtifact": {"methods": ["update_artifact"]}, "UpdateContext": {"methods": ["update_context"]}, "UpdateExecution": {"methods": ["update_execution"]}}}, "grpc-async": {"libraryClient": "MetadataServiceAsyncClient", "rpcs": {"AddContextArtifactsAndExecutions": {"methods": ["add_context_artifacts_and_executions"]}, "AddContextChildren": {"methods": ["add_context_children"]}, "AddExecutionEvents": {"methods": ["add_execution_events"]}, "CreateArtifact": {"methods": ["create_artifact"]}, "CreateContext": {"methods": ["create_context"]}, "CreateExecution": {"methods": ["create_execution"]}, "CreateMetadataSchema": {"methods": ["create_metadata_schema"]}, "CreateMetadataStore": {"methods": ["create_metadata_store"]}, "DeleteArtifact": {"methods": ["delete_artifact"]}, "DeleteContext": {"methods": ["delete_context"]}, "DeleteExecution": {"methods": ["delete_execution"]}, "DeleteMetadataStore": {"methods": ["delete_metadata_store"]}, "GetArtifact": {"methods": ["get_artifact"]}, "GetContext": {"methods": ["get_context"]}, "GetExecution": {"methods": ["get_execution"]}, "GetMetadataSchema": {"methods": ["get_metadata_schema"]}, "GetMetadataStore": {"methods": ["get_metadata_store"]}, "ListArtifacts": {"methods": ["list_artifacts"]}, "ListContexts": {"methods": ["list_contexts"]}, "ListExecutions": {"methods": ["list_executions"]}, "ListMetadataSchemas": {"methods": ["list_metadata_schemas"]}, "ListMetadataStores": {"methods": ["list_metadata_stores"]}, "PurgeArtifacts": {"methods": ["purge_artifacts"]}, "PurgeContexts": {"methods": ["purge_contexts"]}, "PurgeExecutions": {"methods": ["purge_executions"]}, "QueryArtifactLineageSubgraph": {"methods": ["query_artifact_lineage_subgraph"]}, "QueryContextLineageSubgraph": {"methods": ["query_context_lineage_subgraph"]}, "QueryExecutionInputsAndOutputs": {"methods": ["query_execution_inputs_and_outputs"]}, "RemoveContextChildren": {"methods": ["remove_context_children"]}, "UpdateArtifact": {"methods": ["update_artifact"]}, "UpdateContext": {"methods": ["update_context"]}, "UpdateExecution": {"methods": ["update_execution"]}}}, "rest": {"libraryClient": "MetadataServiceClient", "rpcs": {"AddContextArtifactsAndExecutions": {"methods": ["add_context_artifacts_and_executions"]}, "AddContextChildren": {"methods": ["add_context_children"]}, "AddExecutionEvents": {"methods": ["add_execution_events"]}, "CreateArtifact": {"methods": ["create_artifact"]}, "CreateContext": {"methods": ["create_context"]}, "CreateExecution": {"methods": ["create_execution"]}, "CreateMetadataSchema": {"methods": ["create_metadata_schema"]}, "CreateMetadataStore": {"methods": ["create_metadata_store"]}, "DeleteArtifact": {"methods": ["delete_artifact"]}, "DeleteContext": {"methods": ["delete_context"]}, "DeleteExecution": {"methods": ["delete_execution"]}, "DeleteMetadataStore": {"methods": ["delete_metadata_store"]}, "GetArtifact": {"methods": ["get_artifact"]}, "GetContext": {"methods": ["get_context"]}, "GetExecution": {"methods": ["get_execution"]}, "GetMetadataSchema": {"methods": ["get_metadata_schema"]}, "GetMetadataStore": {"methods": ["get_metadata_store"]}, "ListArtifacts": {"methods": ["list_artifacts"]}, "ListContexts": {"methods": ["list_contexts"]}, "ListExecutions": {"methods": ["list_executions"]}, "ListMetadataSchemas": {"methods": ["list_metadata_schemas"]}, "ListMetadataStores": {"methods": ["list_metadata_stores"]}, "PurgeArtifacts": {"methods": ["purge_artifacts"]}, "PurgeContexts": {"methods": ["purge_contexts"]}, "PurgeExecutions": {"methods": ["purge_executions"]}, "QueryArtifactLineageSubgraph": {"methods": ["query_artifact_lineage_subgraph"]}, "QueryContextLineageSubgraph": {"methods": ["query_context_lineage_subgraph"]}, "QueryExecutionInputsAndOutputs": {"methods": ["query_execution_inputs_and_outputs"]}, "RemoveContextChildren": {"methods": ["remove_context_children"]}, "UpdateArtifact": {"methods": ["update_artifact"]}, "UpdateContext": {"methods": ["update_context"]}, "UpdateExecution": {"methods": ["update_execution"]}}}}}, "MigrationService": {"clients": {"grpc": {"libraryClient": "MigrationServiceClient", "rpcs": {"BatchMigrateResources": {"methods": ["batch_migrate_resources"]}, "SearchMigratableResources": {"methods": ["search_migratable_resources"]}}}, "grpc-async": {"libraryClient": "MigrationServiceAsyncClient", "rpcs": {"BatchMigrateResources": {"methods": ["batch_migrate_resources"]}, "SearchMigratableResources": {"methods": ["search_migratable_resources"]}}}, "rest": {"libraryClient": "MigrationServiceClient", "rpcs": {"BatchMigrateResources": {"methods": ["batch_migrate_resources"]}, "SearchMigratableResources": {"methods": ["search_migratable_resources"]}}}}}, "ModelGardenService": {"clients": {"grpc": {"libraryClient": "ModelGardenServiceClient", "rpcs": {"AcceptPublisherModelEula": {"methods": ["accept_publisher_model_eula"]}, "CheckPublisherModelEulaAcceptance": {"methods": ["check_publisher_model_eula_acceptance"]}, "Deploy": {"methods": ["deploy"]}, "DeployPublisherModel": {"methods": ["deploy_publisher_model"]}, "ExportPublisherModel": {"methods": ["export_publisher_model"]}, "GetPublisherModel": {"methods": ["get_publisher_model"]}, "ListPublisherModels": {"methods": ["list_publisher_models"]}}}, "grpc-async": {"libraryClient": "ModelGardenServiceAsyncClient", "rpcs": {"AcceptPublisherModelEula": {"methods": ["accept_publisher_model_eula"]}, "CheckPublisherModelEulaAcceptance": {"methods": ["check_publisher_model_eula_acceptance"]}, "Deploy": {"methods": ["deploy"]}, "DeployPublisherModel": {"methods": ["deploy_publisher_model"]}, "ExportPublisherModel": {"methods": ["export_publisher_model"]}, "GetPublisherModel": {"methods": ["get_publisher_model"]}, "ListPublisherModels": {"methods": ["list_publisher_models"]}}}, "rest": {"libraryClient": "ModelGardenServiceClient", "rpcs": {"AcceptPublisherModelEula": {"methods": ["accept_publisher_model_eula"]}, "CheckPublisherModelEulaAcceptance": {"methods": ["check_publisher_model_eula_acceptance"]}, "Deploy": {"methods": ["deploy"]}, "DeployPublisherModel": {"methods": ["deploy_publisher_model"]}, "ExportPublisherModel": {"methods": ["export_publisher_model"]}, "GetPublisherModel": {"methods": ["get_publisher_model"]}, "ListPublisherModels": {"methods": ["list_publisher_models"]}}}}}, "ModelMonitoringService": {"clients": {"grpc": {"libraryClient": "ModelMonitoringServiceClient", "rpcs": {"CreateModelMonitor": {"methods": ["create_model_monitor"]}, "CreateModelMonitoringJob": {"methods": ["create_model_monitoring_job"]}, "DeleteModelMonitor": {"methods": ["delete_model_monitor"]}, "DeleteModelMonitoringJob": {"methods": ["delete_model_monitoring_job"]}, "GetModelMonitor": {"methods": ["get_model_monitor"]}, "GetModelMonitoringJob": {"methods": ["get_model_monitoring_job"]}, "ListModelMonitoringJobs": {"methods": ["list_model_monitoring_jobs"]}, "ListModelMonitors": {"methods": ["list_model_monitors"]}, "SearchModelMonitoringAlerts": {"methods": ["search_model_monitoring_alerts"]}, "SearchModelMonitoringStats": {"methods": ["search_model_monitoring_stats"]}, "UpdateModelMonitor": {"methods": ["update_model_monitor"]}}}, "grpc-async": {"libraryClient": "ModelMonitoringServiceAsyncClient", "rpcs": {"CreateModelMonitor": {"methods": ["create_model_monitor"]}, "CreateModelMonitoringJob": {"methods": ["create_model_monitoring_job"]}, "DeleteModelMonitor": {"methods": ["delete_model_monitor"]}, "DeleteModelMonitoringJob": {"methods": ["delete_model_monitoring_job"]}, "GetModelMonitor": {"methods": ["get_model_monitor"]}, "GetModelMonitoringJob": {"methods": ["get_model_monitoring_job"]}, "ListModelMonitoringJobs": {"methods": ["list_model_monitoring_jobs"]}, "ListModelMonitors": {"methods": ["list_model_monitors"]}, "SearchModelMonitoringAlerts": {"methods": ["search_model_monitoring_alerts"]}, "SearchModelMonitoringStats": {"methods": ["search_model_monitoring_stats"]}, "UpdateModelMonitor": {"methods": ["update_model_monitor"]}}}, "rest": {"libraryClient": "ModelMonitoringServiceClient", "rpcs": {"CreateModelMonitor": {"methods": ["create_model_monitor"]}, "CreateModelMonitoringJob": {"methods": ["create_model_monitoring_job"]}, "DeleteModelMonitor": {"methods": ["delete_model_monitor"]}, "DeleteModelMonitoringJob": {"methods": ["delete_model_monitoring_job"]}, "GetModelMonitor": {"methods": ["get_model_monitor"]}, "GetModelMonitoringJob": {"methods": ["get_model_monitoring_job"]}, "ListModelMonitoringJobs": {"methods": ["list_model_monitoring_jobs"]}, "ListModelMonitors": {"methods": ["list_model_monitors"]}, "SearchModelMonitoringAlerts": {"methods": ["search_model_monitoring_alerts"]}, "SearchModelMonitoringStats": {"methods": ["search_model_monitoring_stats"]}, "UpdateModelMonitor": {"methods": ["update_model_monitor"]}}}}}, "ModelService": {"clients": {"grpc": {"libraryClient": "ModelServiceClient", "rpcs": {"BatchImportEvaluatedAnnotations": {"methods": ["batch_import_evaluated_annotations"]}, "BatchImportModelEvaluationSlices": {"methods": ["batch_import_model_evaluation_slices"]}, "CopyModel": {"methods": ["copy_model"]}, "DeleteModel": {"methods": ["delete_model"]}, "DeleteModelVersion": {"methods": ["delete_model_version"]}, "ExportModel": {"methods": ["export_model"]}, "GetModel": {"methods": ["get_model"]}, "GetModelEvaluation": {"methods": ["get_model_evaluation"]}, "GetModelEvaluationSlice": {"methods": ["get_model_evaluation_slice"]}, "ImportModelEvaluation": {"methods": ["import_model_evaluation"]}, "ListModelEvaluationSlices": {"methods": ["list_model_evaluation_slices"]}, "ListModelEvaluations": {"methods": ["list_model_evaluations"]}, "ListModelVersionCheckpoints": {"methods": ["list_model_version_checkpoints"]}, "ListModelVersions": {"methods": ["list_model_versions"]}, "ListModels": {"methods": ["list_models"]}, "MergeVersionAliases": {"methods": ["merge_version_aliases"]}, "UpdateExplanationDataset": {"methods": ["update_explanation_dataset"]}, "UpdateModel": {"methods": ["update_model"]}, "UploadModel": {"methods": ["upload_model"]}}}, "grpc-async": {"libraryClient": "ModelServiceAsyncClient", "rpcs": {"BatchImportEvaluatedAnnotations": {"methods": ["batch_import_evaluated_annotations"]}, "BatchImportModelEvaluationSlices": {"methods": ["batch_import_model_evaluation_slices"]}, "CopyModel": {"methods": ["copy_model"]}, "DeleteModel": {"methods": ["delete_model"]}, "DeleteModelVersion": {"methods": ["delete_model_version"]}, "ExportModel": {"methods": ["export_model"]}, "GetModel": {"methods": ["get_model"]}, "GetModelEvaluation": {"methods": ["get_model_evaluation"]}, "GetModelEvaluationSlice": {"methods": ["get_model_evaluation_slice"]}, "ImportModelEvaluation": {"methods": ["import_model_evaluation"]}, "ListModelEvaluationSlices": {"methods": ["list_model_evaluation_slices"]}, "ListModelEvaluations": {"methods": ["list_model_evaluations"]}, "ListModelVersionCheckpoints": {"methods": ["list_model_version_checkpoints"]}, "ListModelVersions": {"methods": ["list_model_versions"]}, "ListModels": {"methods": ["list_models"]}, "MergeVersionAliases": {"methods": ["merge_version_aliases"]}, "UpdateExplanationDataset": {"methods": ["update_explanation_dataset"]}, "UpdateModel": {"methods": ["update_model"]}, "UploadModel": {"methods": ["upload_model"]}}}, "rest": {"libraryClient": "ModelServiceClient", "rpcs": {"BatchImportEvaluatedAnnotations": {"methods": ["batch_import_evaluated_annotations"]}, "BatchImportModelEvaluationSlices": {"methods": ["batch_import_model_evaluation_slices"]}, "CopyModel": {"methods": ["copy_model"]}, "DeleteModel": {"methods": ["delete_model"]}, "DeleteModelVersion": {"methods": ["delete_model_version"]}, "ExportModel": {"methods": ["export_model"]}, "GetModel": {"methods": ["get_model"]}, "GetModelEvaluation": {"methods": ["get_model_evaluation"]}, "GetModelEvaluationSlice": {"methods": ["get_model_evaluation_slice"]}, "ImportModelEvaluation": {"methods": ["import_model_evaluation"]}, "ListModelEvaluationSlices": {"methods": ["list_model_evaluation_slices"]}, "ListModelEvaluations": {"methods": ["list_model_evaluations"]}, "ListModelVersionCheckpoints": {"methods": ["list_model_version_checkpoints"]}, "ListModelVersions": {"methods": ["list_model_versions"]}, "ListModels": {"methods": ["list_models"]}, "MergeVersionAliases": {"methods": ["merge_version_aliases"]}, "UpdateExplanationDataset": {"methods": ["update_explanation_dataset"]}, "UpdateModel": {"methods": ["update_model"]}, "UploadModel": {"methods": ["upload_model"]}}}}}, "NotebookService": {"clients": {"grpc": {"libraryClient": "NotebookServiceClient", "rpcs": {"AssignNotebookRuntime": {"methods": ["assign_notebook_runtime"]}, "CreateNotebookExecutionJob": {"methods": ["create_notebook_execution_job"]}, "CreateNotebookRuntimeTemplate": {"methods": ["create_notebook_runtime_template"]}, "DeleteNotebookExecutionJob": {"methods": ["delete_notebook_execution_job"]}, "DeleteNotebookRuntime": {"methods": ["delete_notebook_runtime"]}, "DeleteNotebookRuntimeTemplate": {"methods": ["delete_notebook_runtime_template"]}, "GetNotebookExecutionJob": {"methods": ["get_notebook_execution_job"]}, "GetNotebookRuntime": {"methods": ["get_notebook_runtime"]}, "GetNotebookRuntimeTemplate": {"methods": ["get_notebook_runtime_template"]}, "ListNotebookExecutionJobs": {"methods": ["list_notebook_execution_jobs"]}, "ListNotebookRuntimeTemplates": {"methods": ["list_notebook_runtime_templates"]}, "ListNotebookRuntimes": {"methods": ["list_notebook_runtimes"]}, "StartNotebookRuntime": {"methods": ["start_notebook_runtime"]}, "StopNotebookRuntime": {"methods": ["stop_notebook_runtime"]}, "UpdateNotebookRuntimeTemplate": {"methods": ["update_notebook_runtime_template"]}, "UpgradeNotebookRuntime": {"methods": ["upgrade_notebook_runtime"]}}}, "grpc-async": {"libraryClient": "NotebookServiceAsyncClient", "rpcs": {"AssignNotebookRuntime": {"methods": ["assign_notebook_runtime"]}, "CreateNotebookExecutionJob": {"methods": ["create_notebook_execution_job"]}, "CreateNotebookRuntimeTemplate": {"methods": ["create_notebook_runtime_template"]}, "DeleteNotebookExecutionJob": {"methods": ["delete_notebook_execution_job"]}, "DeleteNotebookRuntime": {"methods": ["delete_notebook_runtime"]}, "DeleteNotebookRuntimeTemplate": {"methods": ["delete_notebook_runtime_template"]}, "GetNotebookExecutionJob": {"methods": ["get_notebook_execution_job"]}, "GetNotebookRuntime": {"methods": ["get_notebook_runtime"]}, "GetNotebookRuntimeTemplate": {"methods": ["get_notebook_runtime_template"]}, "ListNotebookExecutionJobs": {"methods": ["list_notebook_execution_jobs"]}, "ListNotebookRuntimeTemplates": {"methods": ["list_notebook_runtime_templates"]}, "ListNotebookRuntimes": {"methods": ["list_notebook_runtimes"]}, "StartNotebookRuntime": {"methods": ["start_notebook_runtime"]}, "StopNotebookRuntime": {"methods": ["stop_notebook_runtime"]}, "UpdateNotebookRuntimeTemplate": {"methods": ["update_notebook_runtime_template"]}, "UpgradeNotebookRuntime": {"methods": ["upgrade_notebook_runtime"]}}}, "rest": {"libraryClient": "NotebookServiceClient", "rpcs": {"AssignNotebookRuntime": {"methods": ["assign_notebook_runtime"]}, "CreateNotebookExecutionJob": {"methods": ["create_notebook_execution_job"]}, "CreateNotebookRuntimeTemplate": {"methods": ["create_notebook_runtime_template"]}, "DeleteNotebookExecutionJob": {"methods": ["delete_notebook_execution_job"]}, "DeleteNotebookRuntime": {"methods": ["delete_notebook_runtime"]}, "DeleteNotebookRuntimeTemplate": {"methods": ["delete_notebook_runtime_template"]}, "GetNotebookExecutionJob": {"methods": ["get_notebook_execution_job"]}, "GetNotebookRuntime": {"methods": ["get_notebook_runtime"]}, "GetNotebookRuntimeTemplate": {"methods": ["get_notebook_runtime_template"]}, "ListNotebookExecutionJobs": {"methods": ["list_notebook_execution_jobs"]}, "ListNotebookRuntimeTemplates": {"methods": ["list_notebook_runtime_templates"]}, "ListNotebookRuntimes": {"methods": ["list_notebook_runtimes"]}, "StartNotebookRuntime": {"methods": ["start_notebook_runtime"]}, "StopNotebookRuntime": {"methods": ["stop_notebook_runtime"]}, "UpdateNotebookRuntimeTemplate": {"methods": ["update_notebook_runtime_template"]}, "UpgradeNotebookRuntime": {"methods": ["upgrade_notebook_runtime"]}}}}}, "PersistentResourceService": {"clients": {"grpc": {"libraryClient": "PersistentResourceServiceClient", "rpcs": {"CreatePersistentResource": {"methods": ["create_persistent_resource"]}, "DeletePersistentResource": {"methods": ["delete_persistent_resource"]}, "GetPersistentResource": {"methods": ["get_persistent_resource"]}, "ListPersistentResources": {"methods": ["list_persistent_resources"]}, "RebootPersistentResource": {"methods": ["reboot_persistent_resource"]}, "UpdatePersistentResource": {"methods": ["update_persistent_resource"]}}}, "grpc-async": {"libraryClient": "PersistentResourceServiceAsyncClient", "rpcs": {"CreatePersistentResource": {"methods": ["create_persistent_resource"]}, "DeletePersistentResource": {"methods": ["delete_persistent_resource"]}, "GetPersistentResource": {"methods": ["get_persistent_resource"]}, "ListPersistentResources": {"methods": ["list_persistent_resources"]}, "RebootPersistentResource": {"methods": ["reboot_persistent_resource"]}, "UpdatePersistentResource": {"methods": ["update_persistent_resource"]}}}, "rest": {"libraryClient": "PersistentResourceServiceClient", "rpcs": {"CreatePersistentResource": {"methods": ["create_persistent_resource"]}, "DeletePersistentResource": {"methods": ["delete_persistent_resource"]}, "GetPersistentResource": {"methods": ["get_persistent_resource"]}, "ListPersistentResources": {"methods": ["list_persistent_resources"]}, "RebootPersistentResource": {"methods": ["reboot_persistent_resource"]}, "UpdatePersistentResource": {"methods": ["update_persistent_resource"]}}}}}, "PipelineService": {"clients": {"grpc": {"libraryClient": "PipelineServiceClient", "rpcs": {"BatchCancelPipelineJobs": {"methods": ["batch_cancel_pipeline_jobs"]}, "BatchDeletePipelineJobs": {"methods": ["batch_delete_pipeline_jobs"]}, "CancelPipelineJob": {"methods": ["cancel_pipeline_job"]}, "CancelTrainingPipeline": {"methods": ["cancel_training_pipeline"]}, "CreatePipelineJob": {"methods": ["create_pipeline_job"]}, "CreateTrainingPipeline": {"methods": ["create_training_pipeline"]}, "DeletePipelineJob": {"methods": ["delete_pipeline_job"]}, "DeleteTrainingPipeline": {"methods": ["delete_training_pipeline"]}, "GetPipelineJob": {"methods": ["get_pipeline_job"]}, "GetTrainingPipeline": {"methods": ["get_training_pipeline"]}, "ListPipelineJobs": {"methods": ["list_pipeline_jobs"]}, "ListTrainingPipelines": {"methods": ["list_training_pipelines"]}}}, "grpc-async": {"libraryClient": "PipelineServiceAsyncClient", "rpcs": {"BatchCancelPipelineJobs": {"methods": ["batch_cancel_pipeline_jobs"]}, "BatchDeletePipelineJobs": {"methods": ["batch_delete_pipeline_jobs"]}, "CancelPipelineJob": {"methods": ["cancel_pipeline_job"]}, "CancelTrainingPipeline": {"methods": ["cancel_training_pipeline"]}, "CreatePipelineJob": {"methods": ["create_pipeline_job"]}, "CreateTrainingPipeline": {"methods": ["create_training_pipeline"]}, "DeletePipelineJob": {"methods": ["delete_pipeline_job"]}, "DeleteTrainingPipeline": {"methods": ["delete_training_pipeline"]}, "GetPipelineJob": {"methods": ["get_pipeline_job"]}, "GetTrainingPipeline": {"methods": ["get_training_pipeline"]}, "ListPipelineJobs": {"methods": ["list_pipeline_jobs"]}, "ListTrainingPipelines": {"methods": ["list_training_pipelines"]}}}, "rest": {"libraryClient": "PipelineServiceClient", "rpcs": {"BatchCancelPipelineJobs": {"methods": ["batch_cancel_pipeline_jobs"]}, "BatchDeletePipelineJobs": {"methods": ["batch_delete_pipeline_jobs"]}, "CancelPipelineJob": {"methods": ["cancel_pipeline_job"]}, "CancelTrainingPipeline": {"methods": ["cancel_training_pipeline"]}, "CreatePipelineJob": {"methods": ["create_pipeline_job"]}, "CreateTrainingPipeline": {"methods": ["create_training_pipeline"]}, "DeletePipelineJob": {"methods": ["delete_pipeline_job"]}, "DeleteTrainingPipeline": {"methods": ["delete_training_pipeline"]}, "GetPipelineJob": {"methods": ["get_pipeline_job"]}, "GetTrainingPipeline": {"methods": ["get_training_pipeline"]}, "ListPipelineJobs": {"methods": ["list_pipeline_jobs"]}, "ListTrainingPipelines": {"methods": ["list_training_pipelines"]}}}}}, "PredictionService": {"clients": {"grpc": {"libraryClient": "PredictionServiceClient", "rpcs": {"ChatCompletions": {"methods": ["chat_completions"]}, "CountTokens": {"methods": ["count_tokens"]}, "DirectPredict": {"methods": ["direct_predict"]}, "DirectRawPredict": {"methods": ["direct_raw_predict"]}, "Explain": {"methods": ["explain"]}, "GenerateContent": {"methods": ["generate_content"]}, "Predict": {"methods": ["predict"]}, "RawPredict": {"methods": ["raw_predict"]}, "ServerStreamingPredict": {"methods": ["server_streaming_predict"]}, "StreamDirectPredict": {"methods": ["stream_direct_predict"]}, "StreamDirectRawPredict": {"methods": ["stream_direct_raw_predict"]}, "StreamGenerateContent": {"methods": ["stream_generate_content"]}, "StreamRawPredict": {"methods": ["stream_raw_predict"]}, "StreamingPredict": {"methods": ["streaming_predict"]}, "StreamingRawPredict": {"methods": ["streaming_raw_predict"]}}}, "grpc-async": {"libraryClient": "PredictionServiceAsyncClient", "rpcs": {"ChatCompletions": {"methods": ["chat_completions"]}, "CountTokens": {"methods": ["count_tokens"]}, "DirectPredict": {"methods": ["direct_predict"]}, "DirectRawPredict": {"methods": ["direct_raw_predict"]}, "Explain": {"methods": ["explain"]}, "GenerateContent": {"methods": ["generate_content"]}, "Predict": {"methods": ["predict"]}, "RawPredict": {"methods": ["raw_predict"]}, "ServerStreamingPredict": {"methods": ["server_streaming_predict"]}, "StreamDirectPredict": {"methods": ["stream_direct_predict"]}, "StreamDirectRawPredict": {"methods": ["stream_direct_raw_predict"]}, "StreamGenerateContent": {"methods": ["stream_generate_content"]}, "StreamRawPredict": {"methods": ["stream_raw_predict"]}, "StreamingPredict": {"methods": ["streaming_predict"]}, "StreamingRawPredict": {"methods": ["streaming_raw_predict"]}}}, "rest": {"libraryClient": "PredictionServiceClient", "rpcs": {"ChatCompletions": {"methods": ["chat_completions"]}, "CountTokens": {"methods": ["count_tokens"]}, "DirectPredict": {"methods": ["direct_predict"]}, "DirectRawPredict": {"methods": ["direct_raw_predict"]}, "Explain": {"methods": ["explain"]}, "GenerateContent": {"methods": ["generate_content"]}, "Predict": {"methods": ["predict"]}, "RawPredict": {"methods": ["raw_predict"]}, "ServerStreamingPredict": {"methods": ["server_streaming_predict"]}, "StreamDirectPredict": {"methods": ["stream_direct_predict"]}, "StreamDirectRawPredict": {"methods": ["stream_direct_raw_predict"]}, "StreamGenerateContent": {"methods": ["stream_generate_content"]}, "StreamRawPredict": {"methods": ["stream_raw_predict"]}, "StreamingPredict": {"methods": ["streaming_predict"]}, "StreamingRawPredict": {"methods": ["streaming_raw_predict"]}}}}}, "ReasoningEngineExecutionService": {"clients": {"grpc": {"libraryClient": "ReasoningEngineExecutionServiceClient", "rpcs": {"QueryReasoningEngine": {"methods": ["query_reasoning_engine"]}, "StreamQueryReasoningEngine": {"methods": ["stream_query_reasoning_engine"]}}}, "grpc-async": {"libraryClient": "ReasoningEngineExecutionServiceAsyncClient", "rpcs": {"QueryReasoningEngine": {"methods": ["query_reasoning_engine"]}, "StreamQueryReasoningEngine": {"methods": ["stream_query_reasoning_engine"]}}}, "rest": {"libraryClient": "ReasoningEngineExecutionServiceClient", "rpcs": {"QueryReasoningEngine": {"methods": ["query_reasoning_engine"]}, "StreamQueryReasoningEngine": {"methods": ["stream_query_reasoning_engine"]}}}}}, "ReasoningEngineService": {"clients": {"grpc": {"libraryClient": "ReasoningEngineServiceClient", "rpcs": {"CreateReasoningEngine": {"methods": ["create_reasoning_engine"]}, "DeleteReasoningEngine": {"methods": ["delete_reasoning_engine"]}, "GetReasoningEngine": {"methods": ["get_reasoning_engine"]}, "ListReasoningEngines": {"methods": ["list_reasoning_engines"]}, "UpdateReasoningEngine": {"methods": ["update_reasoning_engine"]}}}, "grpc-async": {"libraryClient": "ReasoningEngineServiceAsyncClient", "rpcs": {"CreateReasoningEngine": {"methods": ["create_reasoning_engine"]}, "DeleteReasoningEngine": {"methods": ["delete_reasoning_engine"]}, "GetReasoningEngine": {"methods": ["get_reasoning_engine"]}, "ListReasoningEngines": {"methods": ["list_reasoning_engines"]}, "UpdateReasoningEngine": {"methods": ["update_reasoning_engine"]}}}, "rest": {"libraryClient": "ReasoningEngineServiceClient", "rpcs": {"CreateReasoningEngine": {"methods": ["create_reasoning_engine"]}, "DeleteReasoningEngine": {"methods": ["delete_reasoning_engine"]}, "GetReasoningEngine": {"methods": ["get_reasoning_engine"]}, "ListReasoningEngines": {"methods": ["list_reasoning_engines"]}, "UpdateReasoningEngine": {"methods": ["update_reasoning_engine"]}}}}}, "ScheduleService": {"clients": {"grpc": {"libraryClient": "ScheduleServiceClient", "rpcs": {"CreateSchedule": {"methods": ["create_schedule"]}, "DeleteSchedule": {"methods": ["delete_schedule"]}, "GetSchedule": {"methods": ["get_schedule"]}, "ListSchedules": {"methods": ["list_schedules"]}, "PauseSchedule": {"methods": ["pause_schedule"]}, "ResumeSchedule": {"methods": ["resume_schedule"]}, "UpdateSchedule": {"methods": ["update_schedule"]}}}, "grpc-async": {"libraryClient": "ScheduleServiceAsyncClient", "rpcs": {"CreateSchedule": {"methods": ["create_schedule"]}, "DeleteSchedule": {"methods": ["delete_schedule"]}, "GetSchedule": {"methods": ["get_schedule"]}, "ListSchedules": {"methods": ["list_schedules"]}, "PauseSchedule": {"methods": ["pause_schedule"]}, "ResumeSchedule": {"methods": ["resume_schedule"]}, "UpdateSchedule": {"methods": ["update_schedule"]}}}, "rest": {"libraryClient": "ScheduleServiceClient", "rpcs": {"CreateSchedule": {"methods": ["create_schedule"]}, "DeleteSchedule": {"methods": ["delete_schedule"]}, "GetSchedule": {"methods": ["get_schedule"]}, "ListSchedules": {"methods": ["list_schedules"]}, "PauseSchedule": {"methods": ["pause_schedule"]}, "ResumeSchedule": {"methods": ["resume_schedule"]}, "UpdateSchedule": {"methods": ["update_schedule"]}}}}}, "SessionService": {"clients": {"grpc": {"libraryClient": "SessionServiceClient", "rpcs": {"AppendEvent": {"methods": ["append_event"]}, "CreateSession": {"methods": ["create_session"]}, "DeleteSession": {"methods": ["delete_session"]}, "GetSession": {"methods": ["get_session"]}, "ListEvents": {"methods": ["list_events"]}, "ListSessions": {"methods": ["list_sessions"]}, "UpdateSession": {"methods": ["update_session"]}}}, "grpc-async": {"libraryClient": "SessionServiceAsyncClient", "rpcs": {"AppendEvent": {"methods": ["append_event"]}, "CreateSession": {"methods": ["create_session"]}, "DeleteSession": {"methods": ["delete_session"]}, "GetSession": {"methods": ["get_session"]}, "ListEvents": {"methods": ["list_events"]}, "ListSessions": {"methods": ["list_sessions"]}, "UpdateSession": {"methods": ["update_session"]}}}, "rest": {"libraryClient": "SessionServiceClient", "rpcs": {"AppendEvent": {"methods": ["append_event"]}, "CreateSession": {"methods": ["create_session"]}, "DeleteSession": {"methods": ["delete_session"]}, "GetSession": {"methods": ["get_session"]}, "ListEvents": {"methods": ["list_events"]}, "ListSessions": {"methods": ["list_sessions"]}, "UpdateSession": {"methods": ["update_session"]}}}}}, "SpecialistPoolService": {"clients": {"grpc": {"libraryClient": "SpecialistPoolServiceClient", "rpcs": {"CreateSpecialistPool": {"methods": ["create_specialist_pool"]}, "DeleteSpecialistPool": {"methods": ["delete_specialist_pool"]}, "GetSpecialistPool": {"methods": ["get_specialist_pool"]}, "ListSpecialistPools": {"methods": ["list_specialist_pools"]}, "UpdateSpecialistPool": {"methods": ["update_specialist_pool"]}}}, "grpc-async": {"libraryClient": "SpecialistPoolServiceAsyncClient", "rpcs": {"CreateSpecialistPool": {"methods": ["create_specialist_pool"]}, "DeleteSpecialistPool": {"methods": ["delete_specialist_pool"]}, "GetSpecialistPool": {"methods": ["get_specialist_pool"]}, "ListSpecialistPools": {"methods": ["list_specialist_pools"]}, "UpdateSpecialistPool": {"methods": ["update_specialist_pool"]}}}, "rest": {"libraryClient": "SpecialistPoolServiceClient", "rpcs": {"CreateSpecialistPool": {"methods": ["create_specialist_pool"]}, "DeleteSpecialistPool": {"methods": ["delete_specialist_pool"]}, "GetSpecialistPool": {"methods": ["get_specialist_pool"]}, "ListSpecialistPools": {"methods": ["list_specialist_pools"]}, "UpdateSpecialistPool": {"methods": ["update_specialist_pool"]}}}}}, "TensorboardService": {"clients": {"grpc": {"libraryClient": "TensorboardServiceClient", "rpcs": {"BatchCreateTensorboardRuns": {"methods": ["batch_create_tensorboard_runs"]}, "BatchCreateTensorboardTimeSeries": {"methods": ["batch_create_tensorboard_time_series"]}, "BatchReadTensorboardTimeSeriesData": {"methods": ["batch_read_tensorboard_time_series_data"]}, "CreateTensorboard": {"methods": ["create_tensorboard"]}, "CreateTensorboardExperiment": {"methods": ["create_tensorboard_experiment"]}, "CreateTensorboardRun": {"methods": ["create_tensorboard_run"]}, "CreateTensorboardTimeSeries": {"methods": ["create_tensorboard_time_series"]}, "DeleteTensorboard": {"methods": ["delete_tensorboard"]}, "DeleteTensorboardExperiment": {"methods": ["delete_tensorboard_experiment"]}, "DeleteTensorboardRun": {"methods": ["delete_tensorboard_run"]}, "DeleteTensorboardTimeSeries": {"methods": ["delete_tensorboard_time_series"]}, "ExportTensorboardTimeSeriesData": {"methods": ["export_tensorboard_time_series_data"]}, "GetTensorboard": {"methods": ["get_tensorboard"]}, "GetTensorboardExperiment": {"methods": ["get_tensorboard_experiment"]}, "GetTensorboardRun": {"methods": ["get_tensorboard_run"]}, "GetTensorboardTimeSeries": {"methods": ["get_tensorboard_time_series"]}, "ListTensorboardExperiments": {"methods": ["list_tensorboard_experiments"]}, "ListTensorboardRuns": {"methods": ["list_tensorboard_runs"]}, "ListTensorboardTimeSeries": {"methods": ["list_tensorboard_time_series"]}, "ListTensorboards": {"methods": ["list_tensorboards"]}, "ReadTensorboardBlobData": {"methods": ["read_tensorboard_blob_data"]}, "ReadTensorboardSize": {"methods": ["read_tensorboard_size"]}, "ReadTensorboardTimeSeriesData": {"methods": ["read_tensorboard_time_series_data"]}, "ReadTensorboardUsage": {"methods": ["read_tensorboard_usage"]}, "UpdateTensorboard": {"methods": ["update_tensorboard"]}, "UpdateTensorboardExperiment": {"methods": ["update_tensorboard_experiment"]}, "UpdateTensorboardRun": {"methods": ["update_tensorboard_run"]}, "UpdateTensorboardTimeSeries": {"methods": ["update_tensorboard_time_series"]}, "WriteTensorboardExperimentData": {"methods": ["write_tensorboard_experiment_data"]}, "WriteTensorboardRunData": {"methods": ["write_tensorboard_run_data"]}}}, "grpc-async": {"libraryClient": "TensorboardServiceAsyncClient", "rpcs": {"BatchCreateTensorboardRuns": {"methods": ["batch_create_tensorboard_runs"]}, "BatchCreateTensorboardTimeSeries": {"methods": ["batch_create_tensorboard_time_series"]}, "BatchReadTensorboardTimeSeriesData": {"methods": ["batch_read_tensorboard_time_series_data"]}, "CreateTensorboard": {"methods": ["create_tensorboard"]}, "CreateTensorboardExperiment": {"methods": ["create_tensorboard_experiment"]}, "CreateTensorboardRun": {"methods": ["create_tensorboard_run"]}, "CreateTensorboardTimeSeries": {"methods": ["create_tensorboard_time_series"]}, "DeleteTensorboard": {"methods": ["delete_tensorboard"]}, "DeleteTensorboardExperiment": {"methods": ["delete_tensorboard_experiment"]}, "DeleteTensorboardRun": {"methods": ["delete_tensorboard_run"]}, "DeleteTensorboardTimeSeries": {"methods": ["delete_tensorboard_time_series"]}, "ExportTensorboardTimeSeriesData": {"methods": ["export_tensorboard_time_series_data"]}, "GetTensorboard": {"methods": ["get_tensorboard"]}, "GetTensorboardExperiment": {"methods": ["get_tensorboard_experiment"]}, "GetTensorboardRun": {"methods": ["get_tensorboard_run"]}, "GetTensorboardTimeSeries": {"methods": ["get_tensorboard_time_series"]}, "ListTensorboardExperiments": {"methods": ["list_tensorboard_experiments"]}, "ListTensorboardRuns": {"methods": ["list_tensorboard_runs"]}, "ListTensorboardTimeSeries": {"methods": ["list_tensorboard_time_series"]}, "ListTensorboards": {"methods": ["list_tensorboards"]}, "ReadTensorboardBlobData": {"methods": ["read_tensorboard_blob_data"]}, "ReadTensorboardSize": {"methods": ["read_tensorboard_size"]}, "ReadTensorboardTimeSeriesData": {"methods": ["read_tensorboard_time_series_data"]}, "ReadTensorboardUsage": {"methods": ["read_tensorboard_usage"]}, "UpdateTensorboard": {"methods": ["update_tensorboard"]}, "UpdateTensorboardExperiment": {"methods": ["update_tensorboard_experiment"]}, "UpdateTensorboardRun": {"methods": ["update_tensorboard_run"]}, "UpdateTensorboardTimeSeries": {"methods": ["update_tensorboard_time_series"]}, "WriteTensorboardExperimentData": {"methods": ["write_tensorboard_experiment_data"]}, "WriteTensorboardRunData": {"methods": ["write_tensorboard_run_data"]}}}, "rest": {"libraryClient": "TensorboardServiceClient", "rpcs": {"BatchCreateTensorboardRuns": {"methods": ["batch_create_tensorboard_runs"]}, "BatchCreateTensorboardTimeSeries": {"methods": ["batch_create_tensorboard_time_series"]}, "BatchReadTensorboardTimeSeriesData": {"methods": ["batch_read_tensorboard_time_series_data"]}, "CreateTensorboard": {"methods": ["create_tensorboard"]}, "CreateTensorboardExperiment": {"methods": ["create_tensorboard_experiment"]}, "CreateTensorboardRun": {"methods": ["create_tensorboard_run"]}, "CreateTensorboardTimeSeries": {"methods": ["create_tensorboard_time_series"]}, "DeleteTensorboard": {"methods": ["delete_tensorboard"]}, "DeleteTensorboardExperiment": {"methods": ["delete_tensorboard_experiment"]}, "DeleteTensorboardRun": {"methods": ["delete_tensorboard_run"]}, "DeleteTensorboardTimeSeries": {"methods": ["delete_tensorboard_time_series"]}, "ExportTensorboardTimeSeriesData": {"methods": ["export_tensorboard_time_series_data"]}, "GetTensorboard": {"methods": ["get_tensorboard"]}, "GetTensorboardExperiment": {"methods": ["get_tensorboard_experiment"]}, "GetTensorboardRun": {"methods": ["get_tensorboard_run"]}, "GetTensorboardTimeSeries": {"methods": ["get_tensorboard_time_series"]}, "ListTensorboardExperiments": {"methods": ["list_tensorboard_experiments"]}, "ListTensorboardRuns": {"methods": ["list_tensorboard_runs"]}, "ListTensorboardTimeSeries": {"methods": ["list_tensorboard_time_series"]}, "ListTensorboards": {"methods": ["list_tensorboards"]}, "ReadTensorboardBlobData": {"methods": ["read_tensorboard_blob_data"]}, "ReadTensorboardSize": {"methods": ["read_tensorboard_size"]}, "ReadTensorboardTimeSeriesData": {"methods": ["read_tensorboard_time_series_data"]}, "ReadTensorboardUsage": {"methods": ["read_tensorboard_usage"]}, "UpdateTensorboard": {"methods": ["update_tensorboard"]}, "UpdateTensorboardExperiment": {"methods": ["update_tensorboard_experiment"]}, "UpdateTensorboardRun": {"methods": ["update_tensorboard_run"]}, "UpdateTensorboardTimeSeries": {"methods": ["update_tensorboard_time_series"]}, "WriteTensorboardExperimentData": {"methods": ["write_tensorboard_experiment_data"]}, "WriteTensorboardRunData": {"methods": ["write_tensorboard_run_data"]}}}}}, "VertexRagDataService": {"clients": {"grpc": {"libraryClient": "VertexRagDataServiceClient", "rpcs": {"CreateRagCorpus": {"methods": ["create_rag_corpus"]}, "DeleteRagCorpus": {"methods": ["delete_rag_corpus"]}, "DeleteRagFile": {"methods": ["delete_rag_file"]}, "GetRagCorpus": {"methods": ["get_rag_corpus"]}, "GetRagEngineConfig": {"methods": ["get_rag_engine_config"]}, "GetRagFile": {"methods": ["get_rag_file"]}, "ImportRagFiles": {"methods": ["import_rag_files"]}, "ListRagCorpora": {"methods": ["list_rag_corpora"]}, "ListRagFiles": {"methods": ["list_rag_files"]}, "UpdateRagCorpus": {"methods": ["update_rag_corpus"]}, "UpdateRagEngineConfig": {"methods": ["update_rag_engine_config"]}, "UploadRagFile": {"methods": ["upload_rag_file"]}}}, "grpc-async": {"libraryClient": "VertexRagDataServiceAsyncClient", "rpcs": {"CreateRagCorpus": {"methods": ["create_rag_corpus"]}, "DeleteRagCorpus": {"methods": ["delete_rag_corpus"]}, "DeleteRagFile": {"methods": ["delete_rag_file"]}, "GetRagCorpus": {"methods": ["get_rag_corpus"]}, "GetRagEngineConfig": {"methods": ["get_rag_engine_config"]}, "GetRagFile": {"methods": ["get_rag_file"]}, "ImportRagFiles": {"methods": ["import_rag_files"]}, "ListRagCorpora": {"methods": ["list_rag_corpora"]}, "ListRagFiles": {"methods": ["list_rag_files"]}, "UpdateRagCorpus": {"methods": ["update_rag_corpus"]}, "UpdateRagEngineConfig": {"methods": ["update_rag_engine_config"]}, "UploadRagFile": {"methods": ["upload_rag_file"]}}}, "rest": {"libraryClient": "VertexRagDataServiceClient", "rpcs": {"CreateRagCorpus": {"methods": ["create_rag_corpus"]}, "DeleteRagCorpus": {"methods": ["delete_rag_corpus"]}, "DeleteRagFile": {"methods": ["delete_rag_file"]}, "GetRagCorpus": {"methods": ["get_rag_corpus"]}, "GetRagEngineConfig": {"methods": ["get_rag_engine_config"]}, "GetRagFile": {"methods": ["get_rag_file"]}, "ImportRagFiles": {"methods": ["import_rag_files"]}, "ListRagCorpora": {"methods": ["list_rag_corpora"]}, "ListRagFiles": {"methods": ["list_rag_files"]}, "UpdateRagCorpus": {"methods": ["update_rag_corpus"]}, "UpdateRagEngineConfig": {"methods": ["update_rag_engine_config"]}, "UploadRagFile": {"methods": ["upload_rag_file"]}}}}}, "VertexRagService": {"clients": {"grpc": {"libraryClient": "VertexRagServiceClient", "rpcs": {"AugmentPrompt": {"methods": ["augment_prompt"]}, "CorroborateContent": {"methods": ["corroborate_content"]}, "RetrieveContexts": {"methods": ["retrieve_contexts"]}}}, "grpc-async": {"libraryClient": "VertexRagServiceAsyncClient", "rpcs": {"AugmentPrompt": {"methods": ["augment_prompt"]}, "CorroborateContent": {"methods": ["corroborate_content"]}, "RetrieveContexts": {"methods": ["retrieve_contexts"]}}}, "rest": {"libraryClient": "VertexRagServiceClient", "rpcs": {"AugmentPrompt": {"methods": ["augment_prompt"]}, "CorroborateContent": {"methods": ["corroborate_content"]}, "RetrieveContexts": {"methods": ["retrieve_contexts"]}}}}}, "VizierService": {"clients": {"grpc": {"libraryClient": "VizierServiceClient", "rpcs": {"AddTrialMeasurement": {"methods": ["add_trial_measurement"]}, "CheckTrialEarlyStoppingState": {"methods": ["check_trial_early_stopping_state"]}, "CompleteTrial": {"methods": ["complete_trial"]}, "CreateStudy": {"methods": ["create_study"]}, "CreateTrial": {"methods": ["create_trial"]}, "DeleteStudy": {"methods": ["delete_study"]}, "DeleteTrial": {"methods": ["delete_trial"]}, "GetStudy": {"methods": ["get_study"]}, "GetTrial": {"methods": ["get_trial"]}, "ListOptimalTrials": {"methods": ["list_optimal_trials"]}, "ListStudies": {"methods": ["list_studies"]}, "ListTrials": {"methods": ["list_trials"]}, "LookupStudy": {"methods": ["lookup_study"]}, "StopTrial": {"methods": ["stop_trial"]}, "SuggestTrials": {"methods": ["suggest_trials"]}}}, "grpc-async": {"libraryClient": "VizierServiceAsyncClient", "rpcs": {"AddTrialMeasurement": {"methods": ["add_trial_measurement"]}, "CheckTrialEarlyStoppingState": {"methods": ["check_trial_early_stopping_state"]}, "CompleteTrial": {"methods": ["complete_trial"]}, "CreateStudy": {"methods": ["create_study"]}, "CreateTrial": {"methods": ["create_trial"]}, "DeleteStudy": {"methods": ["delete_study"]}, "DeleteTrial": {"methods": ["delete_trial"]}, "GetStudy": {"methods": ["get_study"]}, "GetTrial": {"methods": ["get_trial"]}, "ListOptimalTrials": {"methods": ["list_optimal_trials"]}, "ListStudies": {"methods": ["list_studies"]}, "ListTrials": {"methods": ["list_trials"]}, "LookupStudy": {"methods": ["lookup_study"]}, "StopTrial": {"methods": ["stop_trial"]}, "SuggestTrials": {"methods": ["suggest_trials"]}}}, "rest": {"libraryClient": "VizierServiceClient", "rpcs": {"AddTrialMeasurement": {"methods": ["add_trial_measurement"]}, "CheckTrialEarlyStoppingState": {"methods": ["check_trial_early_stopping_state"]}, "CompleteTrial": {"methods": ["complete_trial"]}, "CreateStudy": {"methods": ["create_study"]}, "CreateTrial": {"methods": ["create_trial"]}, "DeleteStudy": {"methods": ["delete_study"]}, "DeleteTrial": {"methods": ["delete_trial"]}, "GetStudy": {"methods": ["get_study"]}, "GetTrial": {"methods": ["get_trial"]}, "ListOptimalTrials": {"methods": ["list_optimal_trials"]}, "ListStudies": {"methods": ["list_studies"]}, "ListTrials": {"methods": ["list_trials"]}, "LookupStudy": {"methods": ["lookup_study"]}, "StopTrial": {"methods": ["stop_trial"]}, "SuggestTrials": {"methods": ["suggest_trials"]}}}}}}}