# -*- coding: utf-8 -*-

# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/audit/bigquery_audit_metadata.proto
# Protobuf Python Version: 4.25.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.iam.v1 import policy_pb2 as google_dot_iam_dot_v1_dot_policy__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.rpc import status_pb2 as google_dot_rpc_dot_status__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n0google/cloud/audit/bigquery_audit_metadata.proto\x12\x12google.cloud.audit\x1a\x1agoogle/iam/v1/policy.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/rpc/status.proto"\xabo\n\x15\x42igQueryAuditMetadata\x12O\n\rjob_insertion\x18\x01 \x01(\x0b\x32\x36.google.cloud.audit.BigQueryAuditMetadata.JobInsertionH\x00\x12I\n\njob_change\x18\x02 \x01(\x0b\x32\x33.google.cloud.audit.BigQueryAuditMetadata.JobChangeH\x00\x12M\n\x0cjob_deletion\x18\x17 \x01(\x0b\x32\x35.google.cloud.audit.BigQueryAuditMetadata.JobDeletionH\x00\x12U\n\x10\x64\x61taset_creation\x18\x03 \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.DatasetCreationH\x00\x12Q\n\x0e\x64\x61taset_change\x18\x04 \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.DatasetChangeH\x00\x12U\n\x10\x64\x61taset_deletion\x18\x05 \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.DatasetDeletionH\x00\x12Q\n\x0etable_creation\x18\x06 \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.TableCreationH\x00\x12M\n\x0ctable_change\x18\x08 \x01(\x0b\x32\x35.google.cloud.audit.BigQueryAuditMetadata.TableChangeH\x00\x12Q\n\x0etable_deletion\x18\t \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.TableDeletionH\x00\x12R\n\x0ftable_data_read\x18\n \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.TableDataReadH\x00\x12V\n\x11table_data_change\x18\x0b \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.TableDataChangeH\x00\x12Q\n\x0emodel_deletion\x18\x0c \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.ModelDeletionH\x00\x12Q\n\x0emodel_creation\x18\r \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.ModelCreationH\x00\x12^\n\x15model_metadata_change\x18\x0e \x01(\x0b\x32=.google.cloud.audit.BigQueryAuditMetadata.ModelMetadataChangeH\x00\x12V\n\x11model_data_change\x18\x0f \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.ModelDataChangeH\x00\x12R\n\x0fmodel_data_read\x18\x13 \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.ModelDataReadH\x00\x12U\n\x10routine_creation\x18\x10 \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.RoutineCreationH\x00\x12Q\n\x0eroutine_change\x18\x11 \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.RoutineChangeH\x00\x12U\n\x10routine_deletion\x18\x12 \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.RoutineDeletionH\x00\x12g\n\x1arow_access_policy_creation\x18\x14 \x01(\x0b\x32\x41.google.cloud.audit.BigQueryAuditMetadata.RowAccessPolicyCreationH\x00\x12\x63\n\x18row_access_policy_change\x18\x15 \x01(\x0b\x32?.google.cloud.audit.BigQueryAuditMetadata.RowAccessPolicyChangeH\x00\x12g\n\x1arow_access_policy_deletion\x18\x16 \x01(\x0b\x32\x41.google.cloud.audit.BigQueryAuditMetadata.RowAccessPolicyDeletionH\x00\x12Q\n\x0eunlink_dataset\x18\x19 \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.UnlinkDatasetH\x00\x12\x61\n\x18\x66irst_party_app_metadata\x18\x18 \x01(\x0b\x32?.google.cloud.audit.BigQueryAuditMetadata.FirstPartyAppMetadata\x1a\xe6\x01\n\x0cJobInsertion\x12:\n\x03job\x18\x01 \x01(\x0b\x32-.google.cloud.audit.BigQueryAuditMetadata.Job\x12M\n\x06reason\x18\x02 \x01(\x0e\x32=.google.cloud.audit.BigQueryAuditMetadata.JobInsertion.Reason"K\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x16\n\x12JOB_INSERT_REQUEST\x10\x01\x12\x11\n\rQUERY_REQUEST\x10\x02\x1a\xce\x01\n\tJobChange\x12\x42\n\x06\x62\x65\x66ore\x18\x01 \x01(\x0e\x32\x32.google.cloud.audit.BigQueryAuditMetadata.JobState\x12\x41\n\x05\x61\x66ter\x18\x02 \x01(\x0e\x32\x32.google.cloud.audit.BigQueryAuditMetadata.JobState\x12:\n\x03job\x18\x03 \x01(\x0b\x32-.google.cloud.audit.BigQueryAuditMetadata.Job\x1a\xa7\x01\n\x0bJobDeletion\x12\x10\n\x08job_name\x18\x01 \x01(\t\x12L\n\x06reason\x18\x02 \x01(\x0e\x32<.google.cloud.audit.BigQueryAuditMetadata.JobDeletion.Reason"8\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x16\n\x12JOB_DELETE_REQUEST\x10\x01\x1a\xf2\x01\n\x0f\x44\x61tasetCreation\x12\x42\n\x07\x64\x61taset\x18\x01 \x01(\x0b\x32\x31.google.cloud.audit.BigQueryAuditMetadata.Dataset\x12P\n\x06reason\x18\x02 \x01(\x0e\<EMAIL>\x12\x10\n\x08job_name\x18\x03 \x01(\t"7\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\n\n\x06\x43REATE\x10\x01\x12\t\n\x05QUERY\x10\x02\x1a\x82\x02\n\rDatasetChange\x12\x42\n\x07\x64\x61taset\x18\x01 \x01(\x0b\x32\x31.google.cloud.audit.BigQueryAuditMetadata.Dataset\x12N\n\x06reason\x18\x02 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.DatasetChange.Reason\x12\x10\n\x08job_name\x18\x03 \x01(\t"K\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\n\n\x06UPDATE\x10\x01\x12\x12\n\x0eSET_IAM_POLICY\x10\x02\x12\t\n\x05QUERY\x10\x03\x1a\xae\x01\n\x0f\x44\x61tasetDeletion\x12P\n\x06reason\x18\x01 \x01(\x0e\<EMAIL>\x12\x10\n\x08job_name\x18\x02 \x01(\t"7\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\n\n\x06\x44\x45LETE\x10\x01\x12\t\n\x05QUERY\x10\x02\x1a\x81\x02\n\rTableCreation\x12>\n\x05table\x18\x01 \x01(\x0b\x32/.google.cloud.audit.BigQueryAuditMetadata.Table\x12N\n\x06reason\x18\x03 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.TableCreation.Reason\x12\x10\n\x08job_name\x18\x04 \x01(\t"N\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x07\n\x03JOB\x10\x01\x12\t\n\x05QUERY\x10\x02\x12\x18\n\x14TABLE_INSERT_REQUEST\x10\x03\x1a\xde\x01\n\rModelCreation\x12>\n\x05model\x18\x01 \x01(\x0b\x32/.google.cloud.audit.BigQueryAuditMetadata.Model\x12N\n\x06reason\x18\x03 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.ModelCreation.Reason\x12\x10\n\x08job_name\x18\x04 \x01(\t"+\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\t\n\x05QUERY\x10\x02\x1a\x82\x02\n\x0fRoutineCreation\x12\x42\n\x07routine\x18\x01 \x01(\x0b\x32\x31.google.cloud.audit.BigQueryAuditMetadata.Routine\x12P\n\x06reason\x18\x03 \x01(\x0e\<EMAIL>\x12\x10\n\x08job_name\x18\x04 \x01(\t"G\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\t\n\x05QUERY\x10\x01\x12\x1a\n\x16ROUTINE_INSERT_REQUEST\x10\x02\x1a\x97\x03\n\rTableDataRead\x12\x0e\n\x06\x66ields\x18\x02 \x03(\t\x12\x18\n\x10\x66ields_truncated\x18\x08 \x01(\x08\x12\x13\n\x0bpolicy_tags\x18\t \x03(\t\x12\x1d\n\x15policy_tags_truncated\x18\n \x01(\x08\x12N\n\x06reason\x18\x03 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.TableDataRead.Reason\x12\x10\n\x08job_name\x18\x04 \x01(\t\x12\x14\n\x0csession_name\x18\x05 \x01(\t"\xaf\x01\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x07\n\x03JOB\x10\x01\x12\x1a\n\x16TABLEDATA_LIST_REQUEST\x10\x02\x12\x1d\n\x19GET_QUERY_RESULTS_REQUEST\x10\x03\x12\x11\n\rQUERY_REQUEST\x10\x04\x12\x17\n\x13\x43REATE_READ_SESSION\x10\x05\x12\x1d\n\x19MATERIALIZED_VIEW_REFRESH\x10\x06\x1a\x90\x02\n\x0bTableChange\x12>\n\x05table\x18\x01 \x01(\x0b\x32/.google.cloud.audit.BigQueryAuditMetadata.Table\x12\x11\n\ttruncated\x18\x04 \x01(\x08\x12L\n\x06reason\x18\x05 \x01(\x0e\x32<.google.cloud.audit.BigQueryAuditMetadata.TableChange.Reason\x12\x10\n\x08job_name\x18\x06 \x01(\t"N\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x18\n\x14TABLE_UPDATE_REQUEST\x10\x01\x12\x07\n\x03JOB\x10\x02\x12\t\n\x05QUERY\x10\x03\x1a\x83\x02\n\x13ModelMetadataChange\x12>\n\x05model\x18\x01 \x01(\x0b\x32/.google.cloud.audit.BigQueryAuditMetadata.Model\x12T\n\x06reason\x18\x02 \x01(\x0e\x32\x44.google.cloud.audit.BigQueryAuditMetadata.ModelMetadataChange.Reason\x12\x10\n\x08job_name\x18\x03 \x01(\t"D\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x17\n\x13MODEL_PATCH_REQUEST\x10\x01\x12\t\n\x05QUERY\x10\x02\x1a\xfe\x01\n\rRoutineChange\x12\x42\n\x07routine\x18\x01 \x01(\x0b\x32\x31.google.cloud.audit.BigQueryAuditMetadata.Routine\x12N\n\x06reason\x18\x03 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.RoutineChange.Reason\x12\x10\n\x08job_name\x18\x04 \x01(\t"G\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\t\n\x05QUERY\x10\x01\x12\x1a\n\x16ROUTINE_UPDATE_REQUEST\x10\x02\x1a\xba\x02\n\x0fTableDataChange\x12\x1a\n\x12\x64\x65leted_rows_count\x18\x01 \x01(\x03\x12\x1b\n\x13inserted_rows_count\x18\x02 \x01(\x03\x12\x11\n\ttruncated\x18\x03 \x01(\x08\x12P\n\x06reason\x18\x04 \x01(\x0e\<EMAIL>\x12\x10\n\x08job_name\x18\x05 \x01(\t\x12\x13\n\x0bstream_name\x18\x06 \x01(\t"b\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x07\n\x03JOB\x10\x01\x12\t\n\x05QUERY\x10\x02\x12\x1d\n\x19MATERIALIZED_VIEW_REFRESH\x10\x03\x12\r\n\tWRITE_API\x10\x04\x1a\xa2\x01\n\x0fModelDataChange\x12P\n\x06reason\x18\x01 \x01(\x0e\<EMAIL>\x12\x10\n\x08job_name\x18\x02 \x01(\t"+\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\t\n\x05QUERY\x10\x01\x1a\x9c\x01\n\rModelDataRead\x12N\n\x06reason\x18\x01 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.ModelDataRead.Reason\x12\x10\n\x08job_name\x18\x02 \x01(\t")\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x07\n\x03JOB\x10\x01\x1a\xc5\x01\n\rTableDeletion\x12N\n\x06reason\x18\x01 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.TableDeletion.Reason\x12\x10\n\x08job_name\x18\x02 \x01(\t"R\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x18\n\x14TABLE_DELETE_REQUEST\x10\x02\x12\x0b\n\x07\x45XPIRED\x10\x03\x12\t\n\x05QUERY\x10\x04\x1a\xc5\x01\n\rModelDeletion\x12N\n\x06reason\x18\x01 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.ModelDeletion.Reason\x12\x10\n\x08job_name\x18\x02 \x01(\t"R\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x18\n\x14MODEL_DELETE_REQUEST\x10\x01\x12\x0b\n\x07\x45XPIRED\x10\x02\x12\t\n\x05QUERY\x10\x03\x1a\x82\x02\n\x0fRoutineDeletion\x12\x42\n\x07routine\x18\x01 \x01(\x0b\x32\x31.google.cloud.audit.BigQueryAuditMetadata.Routine\x12P\n\x06reason\x18\x03 \x01(\x0e\<EMAIL>\x12\x10\n\x08job_name\x18\x04 \x01(\t"G\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\t\n\x05QUERY\x10\x01\x12\x1a\n\x16ROUTINE_DELETE_REQUEST\x10\x02\x1a\x81\x01\n\x17RowAccessPolicyCreation\x12T\n\x11row_access_policy\x18\x01 \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.RowAccessPolicy\x12\x10\n\x08job_name\x18\x02 \x01(\t\x1a\x7f\n\x15RowAccessPolicyChange\x12T\n\x11row_access_policy\x18\x01 \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.RowAccessPolicy\x12\x10\n\x08job_name\x18\x02 \x01(\t\x1a\xac\x01\n\x17RowAccessPolicyDeletion\x12V\n\x13row_access_policies\x18\x01 \x03(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.RowAccessPolicy\x12\x10\n\x08job_name\x18\x02 \x01(\t\x12\'\n\x1f\x61ll_row_access_policies_dropped\x18\x03 \x01(\x08\x1a\xc1\x01\n\rUnlinkDataset\x12\x16\n\x0elinked_dataset\x18\x01 \x01(\t\x12\x16\n\x0esource_dataset\x18\x02 \x01(\t\x12N\n\x06reason\x18\x03 \x01(\x0e\x32>.google.cloud.audit.BigQueryAuditMetadata.UnlinkDataset.Reason"0\n\x06Reason\x12\x16\n\x12REASON_UNSPECIFIED\x10\x00\x12\x0e\n\nUNLINK_API\x10\x01\x1a\xf0\x01\n\x03Job\x12\x10\n\x08job_name\x18\x01 \x01(\t\x12G\n\njob_config\x18\x02 \x01(\x0b\x32\x33.google.cloud.audit.BigQueryAuditMetadata.JobConfig\x12G\n\njob_status\x18\x03 \x01(\x0b\x32\x33.google.cloud.audit.BigQueryAuditMetadata.JobStatus\x12\x45\n\tjob_stats\x18\x04 \x01(\x0b\x32\x32.google.cloud.audit.BigQueryAuditMetadata.JobStats\x1a\xe8\x12\n\tJobConfig\x12\x46\n\x04type\x18\x01 \x01(\x0e\x32\x38.google.cloud.audit.BigQueryAuditMetadata.JobConfig.Type\x12Q\n\x0cquery_config\x18\x02 \x01(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.JobConfig.QueryH\x00\x12O\n\x0bload_config\x18\x03 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.JobConfig.LoadH\x00\x12U\n\x0e\x65xtract_config\x18\x04 \x01(\x0b\x32;.google.cloud.audit.BigQueryAuditMetadata.JobConfig.ExtractH\x00\x12Z\n\x11table_copy_config\x18\x05 \x01(\x0b\x32=.google.cloud.audit.BigQueryAuditMetadata.JobConfig.TableCopyH\x00\x12O\n\x06labels\x18\x06 \x03(\x0b\x32?.google.cloud.audit.BigQueryAuditMetadata.JobConfig.LabelsEntry\x1a\xc3\x05\n\x05Query\x12\r\n\x05query\x18\x01 \x01(\t\x12\x17\n\x0fquery_truncated\x18\n \x01(\x08\x12\x19\n\x11\x64\x65stination_table\x18\x02 \x01(\t\x12W\n\x12\x63reate_disposition\x18\x03 \x01(\x0e\x32;.google.cloud.audit.BigQueryAuditMetadata.CreateDisposition\x12U\n\x11write_disposition\x18\x04 \x01(\x0e\x32:.google.cloud.audit.BigQueryAuditMetadata.WriteDisposition\x12\x17\n\x0f\x64\x65\x66\x61ult_dataset\x18\x05 \x01(\t\x12T\n\x11table_definitions\x18\x06 \x03(\x0b\x32\x39.google.cloud.audit.BigQueryAuditMetadata.TableDefinition\x12T\n\x08priority\x18\x07 \x01(\x0e\x32\x42.google.cloud.audit.BigQueryAuditMetadata.JobConfig.Query.Priority\x12^\n\x1c\x64\x65stination_table_encryption\x18\x08 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.EncryptionInfo\x12T\n\x0estatement_type\x18\t \x01(\x0e\x32<.google.cloud.audit.BigQueryAuditMetadata.QueryStatementType"L\n\x08Priority\x12\x18\n\x14PRIORITY_UNSPECIFIED\x10\x00\x12\x15\n\x11QUERY_INTERACTIVE\x10\x01\x12\x0f\n\x0bQUERY_BATCH\x10\x02\x1a\x99\x03\n\x04Load\x12\x13\n\x0bsource_uris\x18\x01 \x03(\t\x12\x1d\n\x15source_uris_truncated\x18\x07 \x01(\x08\x12\x13\n\x0bschema_json\x18\x02 \x01(\t\x12\x1d\n\x15schema_json_truncated\x18\x08 \x01(\x08\x12\x19\n\x11\x64\x65stination_table\x18\x03 \x01(\t\x12W\n\x12\x63reate_disposition\x18\x04 \x01(\x0e\x32;.google.cloud.audit.BigQueryAuditMetadata.CreateDisposition\x12U\n\x11write_disposition\x18\x05 \x01(\x0e\x32:.google.cloud.audit.BigQueryAuditMetadata.WriteDisposition\x12^\n\x1c\x64\x65stination_table_encryption\x18\x06 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.EncryptionInfo\x1a\x81\x01\n\x07\x45xtract\x12\x18\n\x10\x64\x65stination_uris\x18\x01 \x03(\t\x12"\n\x1a\x64\x65stination_uris_truncated\x18\x03 \x01(\x08\x12\x16\n\x0csource_table\x18\x02 \x01(\tH\x00\x12\x16\n\x0csource_model\x18\x04 \x01(\tH\x00\x42\x08\n\x06source\x1a\x80\x04\n\tTableCopy\x12\x15\n\rsource_tables\x18\x01 \x03(\t\x12\x1f\n\x17source_tables_truncated\x18\x06 \x01(\x08\x12\x19\n\x11\x64\x65stination_table\x18\x02 \x01(\t\x12W\n\x12\x63reate_disposition\x18\x03 \x01(\x0e\x32;.google.cloud.audit.BigQueryAuditMetadata.CreateDisposition\x12U\n\x11write_disposition\x18\x04 \x01(\x0e\x32:.google.cloud.audit.BigQueryAuditMetadata.WriteDisposition\x12^\n\x1c\x64\x65stination_table_encryption\x18\x05 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.EncryptionInfo\x12O\n\x0eoperation_type\x18\x07 \x01(\x0e\x32\x37.google.cloud.audit.BigQueryAuditMetadata.OperationType\x12?\n\x1b\x64\x65stination_expiration_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01"I\n\x04Type\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05QUERY\x10\x01\x12\x08\n\x04\x43OPY\x10\x02\x12\n\n\x06\x45XPORT\x10\x03\x12\n\n\x06IMPORT\x10\x04\x42\x08\n\x06\x63onfig\x1a\x34\n\x0fTableDefinition\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bsource_uris\x18\x02 \x03(\t\x1a\xa0\x01\n\tJobStatus\x12\x45\n\tjob_state\x18\x01 \x01(\x0e\x32\x32.google.cloud.audit.BigQueryAuditMetadata.JobState\x12(\n\x0c\x65rror_result\x18\x02 \x01(\x0b\x32\x12.google.rpc.Status\x12"\n\x06\x65rrors\x18\x03 \x03(\x0b\x32\x12.google.rpc.Status\x1a\xaa\x07\n\x08JobStats\x12/\n\x0b\x63reate_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nstart_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12O\n\x0bquery_stats\x18\x08 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.JobStats.QueryH\x00\x12M\n\nload_stats\x18\t \x01(\x0b\x32\x37.google.cloud.audit.BigQueryAuditMetadata.JobStats.LoadH\x00\x12S\n\rextract_stats\x18\r \x01(\x0b\x32:.google.cloud.audit.BigQueryAuditMetadata.JobStats.ExtractH\x00\x12\x15\n\rtotal_slot_ms\x18\n \x01(\x03\x12j\n\x11reservation_usage\x18\x0b \x03(\x0b\x32K.google.cloud.audit.BigQueryAuditMetadata.JobStats.ReservationResourceUsageB\x02\x18\x01\x12\x13\n\x0breservation\x18\x0e \x01(\t\x12\x17\n\x0fparent_job_name\x18\x0c \x01(\t\x1a\xd7\x01\n\x05Query\x12\x1d\n\x15total_processed_bytes\x18\x01 \x01(\x03\x12\x1a\n\x12total_billed_bytes\x18\x02 \x01(\x03\x12\x14\n\x0c\x62illing_tier\x18\x03 \x01(\x05\x12\x19\n\x11referenced_tables\x18\x06 \x03(\t\x12\x18\n\x10referenced_views\x18\x07 \x03(\t\x12\x1b\n\x13referenced_routines\x18\n \x03(\t\x12\x18\n\x10output_row_count\x18\x08 \x01(\x03\x12\x11\n\tcache_hit\x18\t \x01(\x08\x1a"\n\x04Load\x12\x1a\n\x12total_output_bytes\x18\x01 \x01(\x03\x1a$\n\x07\x45xtract\x12\x19\n\x11total_input_bytes\x18\x01 \x01(\x03\x1a\x39\n\x18ReservationResourceUsage\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07slot_ms\x18\x02 \x01(\x03\x42\n\n\x08\x65xtended\x1a\xfa\x03\n\x05Table\x12\x12\n\ntable_name\x18\x01 \x01(\t\x12H\n\ntable_info\x18\n \x01(\x0b\x32\x34.google.cloud.audit.BigQueryAuditMetadata.EntityInfo\x12\x13\n\x0bschema_json\x18\x03 \x01(\t\x12\x1d\n\x15schema_json_truncated\x18\x0b \x01(\x08\x12K\n\x04view\x18\x04 \x01(\x0b\x32=.google.cloud.audit.BigQueryAuditMetadata.TableViewDefinition\x12/\n\x0b\x65xpire_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x63reate_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0bupdate_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rtruncate_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12L\n\nencryption\x18\t \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.EncryptionInfo\x1a\xc6\x02\n\x05Model\x12\x12\n\nmodel_name\x18\x01 \x01(\t\x12H\n\nmodel_info\x18\x02 \x01(\x0b\x32\x34.google.cloud.audit.BigQueryAuditMetadata.EntityInfo\x12/\n\x0b\x65xpire_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x63reate_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0bupdate_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12L\n\nencryption\x18\x08 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.EncryptionInfo\x1a\x81\x01\n\x07Routine\x12\x14\n\x0croutine_name\x18\x01 \x01(\t\x12/\n\x0b\x63reate_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0bupdate_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x1a\xb9\x01\n\nEntityInfo\x12\x15\n\rfriendly_name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12P\n\x06labels\x18\x03 \x03(\x0b\<EMAIL>\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a=\n\x13TableViewDefinition\x12\r\n\x05query\x18\x01 \x01(\t\x12\x17\n\x0fquery_truncated\x18\x02 \x01(\x08\x1a\xc4\x03\n\x07\x44\x61taset\x12\x14\n\x0c\x64\x61taset_name\x18\x01 \x01(\t\x12J\n\x0c\x64\x61taset_info\x18\x07 \x01(\x0b\x32\x34.google.cloud.audit.BigQueryAuditMetadata.EntityInfo\x12/\n\x0b\x63reate_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0bupdate_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x42\n\x03\x61\x63l\x18\x05 \x01(\x0b\x32\x35.google.cloud.audit.BigQueryAuditMetadata.BigQueryAcl\x12@\n\x1d\x64\x65\x66\x61ult_table_expire_duration\x18\x06 \x01(\x0b\x32\x19.google.protobuf.Duration\x12T\n\x12\x64\x65\x66\x61ult_encryption\x18\x08 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.EncryptionInfo\x12\x19\n\x11\x64\x65\x66\x61ult_collation\x18\t \x01(\t\x1aN\n\x0b\x42igQueryAcl\x12%\n\x06policy\x18\x01 \x01(\x0b\x32\x15.google.iam.v1.Policy\x12\x18\n\x10\x61uthorized_views\x18\x02 \x03(\t\x1a&\n\x0e\x45ncryptionInfo\x12\x14\n\x0ckms_key_name\x18\x01 \x01(\t\x1a\x31\n\x0fRowAccessPolicy\x12\x1e\n\x16row_access_policy_name\x18\x01 \x01(\t\x1ax\n\x15\x46irstPartyAppMetadata\x12S\n\x0fsheets_metadata\x18\x01 \x01(\x0b\x32\x38.google.cloud.audit.BigQueryAuditMetadata.SheetsMetadataH\x00\x42\n\n\x08metadata\x1a \n\x0eSheetsMetadata\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\t"_\n\x11\x43reateDisposition\x12"\n\x1e\x43REATE_DISPOSITION_UNSPECIFIED\x10\x00\x12\x10\n\x0c\x43REATE_NEVER\x10\x01\x12\x14\n\x10\x43REATE_IF_NEEDED\x10\x02"l\n\x10WriteDisposition\x12!\n\x1dWRITE_DISPOSITION_UNSPECIFIED\x10\x00\x12\x0f\n\x0bWRITE_EMPTY\x10\x01\x12\x12\n\x0eWRITE_TRUNCATE\x10\x02\x12\x10\n\x0cWRITE_APPEND\x10\x03"T\n\rOperationType\x12\x1e\n\x1aOPERATION_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04\x43OPY\x10\x01\x12\x0c\n\x08SNAPSHOT\x10\x02\x12\x0b\n\x07RESTORE\x10\x03"I\n\x08JobState\x12\x19\n\x15JOB_STATE_UNSPECIFIED\x10\x00\x12\x0b\n\x07PENDING\x10\x01\x12\x0b\n\x07RUNNING\x10\x02\x12\x08\n\x04\x44ONE\x10\x03"\xf9\x05\n\x12QueryStatementType\x12$\n QUERY_STATEMENT_TYPE_UNSPECIFIED\x10\x00\x12\n\n\x06SELECT\x10\x01\x12\n\n\x06\x41SSERT\x10\x17\x12\n\n\x06INSERT\x10\x02\x12\n\n\x06UPDATE\x10\x03\x12\n\n\x06\x44\x45LETE\x10\x04\x12\t\n\x05MERGE\x10\x05\x12\x10\n\x0c\x43REATE_TABLE\x10\x06\x12\x1a\n\x16\x43REATE_TABLE_AS_SELECT\x10\x07\x12\x0f\n\x0b\x43REATE_VIEW\x10\x08\x12\x10\n\x0c\x43REATE_MODEL\x10\t\x12\x1c\n\x18\x43REATE_MATERIALIZED_VIEW\x10\r\x12\x13\n\x0f\x43REATE_FUNCTION\x10\x0e\x12\x19\n\x15\x43REATE_TABLE_FUNCTION\x10\x38\x12\x14\n\x10\x43REATE_PROCEDURE\x10\x14\x12\x1c\n\x18\x43REATE_ROW_ACCESS_POLICY\x10\x18\x12\x11\n\rCREATE_SCHEMA\x10\x35\x12\x19\n\x15\x43REATE_SNAPSHOT_TABLE\x10;\x12\x0e\n\nDROP_TABLE\x10\n\x12\x17\n\x13\x44ROP_EXTERNAL_TABLE\x10!\x12\r\n\tDROP_VIEW\x10\x0b\x12\x0e\n\nDROP_MODEL\x10\x0c\x12\x1a\n\x16\x44ROP_MATERIALIZED_VIEW\x10\x0f\x12\x11\n\rDROP_FUNCTION\x10\x10\x12\x12\n\x0e\x44ROP_PROCEDURE\x10\x15\x12\x0f\n\x0b\x44ROP_SCHEMA\x10\x36\x12\x1a\n\x16\x44ROP_ROW_ACCESS_POLICY\x10\x19\x12\x17\n\x13\x44ROP_SNAPSHOT_TABLE\x10>\x12\x0f\n\x0b\x41LTER_TABLE\x10\x11\x12\x0e\n\nALTER_VIEW\x10\x12\x12\x1b\n\x17\x41LTER_MATERIALIZED_VIEW\x10\x16\x12\x10\n\x0c\x41LTER_SCHEMA\x10\x37\x12\n\n\x06SCRIPT\x10\x13\x12\x12\n\x0eTRUNCATE_TABLE\x10\x1a\x12\x19\n\x15\x43REATE_EXTERNAL_TABLE\x10\x1b\x12\x0f\n\x0b\x45XPORT_DATA\x10\x1c\x12\x08\n\x04\x43\x41LL\x10\x1d\x42\x07\n\x05\x65ventB\x9f\x01\n\x16\x63om.google.cloud.auditB\x1a\x42igQueryAuditMetadataProtoP\x01Z7google.golang.org/genproto/googleapis/cloud/audit;audit\xa2\x02\x03GCA\xaa\x02\x12Google.Cloud.Audit\xca\x02\x12Google\\Cloud\\Auditb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "google.cloud.audit.bigquery_audit_metadata_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals[
        "DESCRIPTOR"
    ]._serialized_options = b"\n\026com.google.cloud.auditB\032BigQueryAuditMetadataProtoP\001Z7google.golang.org/genproto/googleapis/cloud/audit;audit\242\002\003GCA\252\002\022Google.Cloud.Audit\312\002\022Google\\Cloud\\Audit"
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_LABELSENTRY"]._options = None
    _globals[
        "_BIGQUERYAUDITMETADATA_JOBCONFIG_LABELSENTRY"
    ]._serialized_options = b"8\001"
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS"].fields_by_name[
        "reservation_usage"
    ]._options = None
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS"].fields_by_name[
        "reservation_usage"
    ]._serialized_options = b"\030\001"
    _globals["_BIGQUERYAUDITMETADATA_ENTITYINFO_LABELSENTRY"]._options = None
    _globals[
        "_BIGQUERYAUDITMETADATA_ENTITYINFO_LABELSENTRY"
    ]._serialized_options = b"8\001"
    _globals["_BIGQUERYAUDITMETADATA"]._serialized_start = 191
    _globals["_BIGQUERYAUDITMETADATA"]._serialized_end = 14442
    _globals["_BIGQUERYAUDITMETADATA_JOBINSERTION"]._serialized_start = 2310
    _globals["_BIGQUERYAUDITMETADATA_JOBINSERTION"]._serialized_end = 2540
    _globals["_BIGQUERYAUDITMETADATA_JOBINSERTION_REASON"]._serialized_start = 2465
    _globals["_BIGQUERYAUDITMETADATA_JOBINSERTION_REASON"]._serialized_end = 2540
    _globals["_BIGQUERYAUDITMETADATA_JOBCHANGE"]._serialized_start = 2543
    _globals["_BIGQUERYAUDITMETADATA_JOBCHANGE"]._serialized_end = 2749
    _globals["_BIGQUERYAUDITMETADATA_JOBDELETION"]._serialized_start = 2752
    _globals["_BIGQUERYAUDITMETADATA_JOBDELETION"]._serialized_end = 2919
    _globals["_BIGQUERYAUDITMETADATA_JOBDELETION_REASON"]._serialized_start = 2863
    _globals["_BIGQUERYAUDITMETADATA_JOBDELETION_REASON"]._serialized_end = 2919
    _globals["_BIGQUERYAUDITMETADATA_DATASETCREATION"]._serialized_start = 2922
    _globals["_BIGQUERYAUDITMETADATA_DATASETCREATION"]._serialized_end = 3164
    _globals["_BIGQUERYAUDITMETADATA_DATASETCREATION_REASON"]._serialized_start = 3109
    _globals["_BIGQUERYAUDITMETADATA_DATASETCREATION_REASON"]._serialized_end = 3164
    _globals["_BIGQUERYAUDITMETADATA_DATASETCHANGE"]._serialized_start = 3167
    _globals["_BIGQUERYAUDITMETADATA_DATASETCHANGE"]._serialized_end = 3425
    _globals["_BIGQUERYAUDITMETADATA_DATASETCHANGE_REASON"]._serialized_start = 3350
    _globals["_BIGQUERYAUDITMETADATA_DATASETCHANGE_REASON"]._serialized_end = 3425
    _globals["_BIGQUERYAUDITMETADATA_DATASETDELETION"]._serialized_start = 3428
    _globals["_BIGQUERYAUDITMETADATA_DATASETDELETION"]._serialized_end = 3602
    _globals["_BIGQUERYAUDITMETADATA_DATASETDELETION_REASON"]._serialized_start = 3547
    _globals["_BIGQUERYAUDITMETADATA_DATASETDELETION_REASON"]._serialized_end = 3602
    _globals["_BIGQUERYAUDITMETADATA_TABLECREATION"]._serialized_start = 3605
    _globals["_BIGQUERYAUDITMETADATA_TABLECREATION"]._serialized_end = 3862
    _globals["_BIGQUERYAUDITMETADATA_TABLECREATION_REASON"]._serialized_start = 3784
    _globals["_BIGQUERYAUDITMETADATA_TABLECREATION_REASON"]._serialized_end = 3862
    _globals["_BIGQUERYAUDITMETADATA_MODELCREATION"]._serialized_start = 3865
    _globals["_BIGQUERYAUDITMETADATA_MODELCREATION"]._serialized_end = 4087
    _globals["_BIGQUERYAUDITMETADATA_MODELCREATION_REASON"]._serialized_start = 4044
    _globals["_BIGQUERYAUDITMETADATA_MODELCREATION_REASON"]._serialized_end = 4087
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECREATION"]._serialized_start = 4090
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECREATION"]._serialized_end = 4348
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECREATION_REASON"]._serialized_start = 4277
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECREATION_REASON"]._serialized_end = 4348
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATAREAD"]._serialized_start = 4351
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATAREAD"]._serialized_end = 4758
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATAREAD_REASON"]._serialized_start = 4583
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATAREAD_REASON"]._serialized_end = 4758
    _globals["_BIGQUERYAUDITMETADATA_TABLECHANGE"]._serialized_start = 4761
    _globals["_BIGQUERYAUDITMETADATA_TABLECHANGE"]._serialized_end = 5033
    _globals["_BIGQUERYAUDITMETADATA_TABLECHANGE_REASON"]._serialized_start = 4955
    _globals["_BIGQUERYAUDITMETADATA_TABLECHANGE_REASON"]._serialized_end = 5033
    _globals["_BIGQUERYAUDITMETADATA_MODELMETADATACHANGE"]._serialized_start = 5036
    _globals["_BIGQUERYAUDITMETADATA_MODELMETADATACHANGE"]._serialized_end = 5295
    _globals[
        "_BIGQUERYAUDITMETADATA_MODELMETADATACHANGE_REASON"
    ]._serialized_start = 5227
    _globals["_BIGQUERYAUDITMETADATA_MODELMETADATACHANGE_REASON"]._serialized_end = 5295
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECHANGE"]._serialized_start = 5298
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECHANGE"]._serialized_end = 5552
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECHANGE_REASON"]._serialized_start = 5481
    _globals["_BIGQUERYAUDITMETADATA_ROUTINECHANGE_REASON"]._serialized_end = 5552
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATACHANGE"]._serialized_start = 5555
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATACHANGE"]._serialized_end = 5869
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATACHANGE_REASON"]._serialized_start = 5771
    _globals["_BIGQUERYAUDITMETADATA_TABLEDATACHANGE_REASON"]._serialized_end = 5869
    _globals["_BIGQUERYAUDITMETADATA_MODELDATACHANGE"]._serialized_start = 5872
    _globals["_BIGQUERYAUDITMETADATA_MODELDATACHANGE"]._serialized_end = 6034
    _globals["_BIGQUERYAUDITMETADATA_MODELDATACHANGE_REASON"]._serialized_start = 4277
    _globals["_BIGQUERYAUDITMETADATA_MODELDATACHANGE_REASON"]._serialized_end = 4320
    _globals["_BIGQUERYAUDITMETADATA_MODELDATAREAD"]._serialized_start = 6037
    _globals["_BIGQUERYAUDITMETADATA_MODELDATAREAD"]._serialized_end = 6193
    _globals["_BIGQUERYAUDITMETADATA_MODELDATAREAD_REASON"]._serialized_start = 3784
    _globals["_BIGQUERYAUDITMETADATA_MODELDATAREAD_REASON"]._serialized_end = 3825
    _globals["_BIGQUERYAUDITMETADATA_TABLEDELETION"]._serialized_start = 6196
    _globals["_BIGQUERYAUDITMETADATA_TABLEDELETION"]._serialized_end = 6393
    _globals["_BIGQUERYAUDITMETADATA_TABLEDELETION_REASON"]._serialized_start = 6311
    _globals["_BIGQUERYAUDITMETADATA_TABLEDELETION_REASON"]._serialized_end = 6393
    _globals["_BIGQUERYAUDITMETADATA_MODELDELETION"]._serialized_start = 6396
    _globals["_BIGQUERYAUDITMETADATA_MODELDELETION"]._serialized_end = 6593
    _globals["_BIGQUERYAUDITMETADATA_MODELDELETION_REASON"]._serialized_start = 6511
    _globals["_BIGQUERYAUDITMETADATA_MODELDELETION_REASON"]._serialized_end = 6593
    _globals["_BIGQUERYAUDITMETADATA_ROUTINEDELETION"]._serialized_start = 6596
    _globals["_BIGQUERYAUDITMETADATA_ROUTINEDELETION"]._serialized_end = 6854
    _globals["_BIGQUERYAUDITMETADATA_ROUTINEDELETION_REASON"]._serialized_start = 6783
    _globals["_BIGQUERYAUDITMETADATA_ROUTINEDELETION_REASON"]._serialized_end = 6854
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICYCREATION"]._serialized_start = 6857
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICYCREATION"]._serialized_end = 6986
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICYCHANGE"]._serialized_start = 6988
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICYCHANGE"]._serialized_end = 7115
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICYDELETION"]._serialized_start = 7118
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICYDELETION"]._serialized_end = 7290
    _globals["_BIGQUERYAUDITMETADATA_UNLINKDATASET"]._serialized_start = 7293
    _globals["_BIGQUERYAUDITMETADATA_UNLINKDATASET"]._serialized_end = 7486
    _globals["_BIGQUERYAUDITMETADATA_UNLINKDATASET_REASON"]._serialized_start = 7438
    _globals["_BIGQUERYAUDITMETADATA_UNLINKDATASET_REASON"]._serialized_end = 7486
    _globals["_BIGQUERYAUDITMETADATA_JOB"]._serialized_start = 7489
    _globals["_BIGQUERYAUDITMETADATA_JOB"]._serialized_end = 7729
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG"]._serialized_start = 7732
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG"]._serialized_end = 10140
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_QUERY"]._serialized_start = 8242
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_QUERY"]._serialized_end = 8949
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_QUERY_PRIORITY"]._serialized_start = 8873
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_QUERY_PRIORITY"]._serialized_end = 8949
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_LOAD"]._serialized_start = 8952
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_LOAD"]._serialized_end = 9361
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_EXTRACT"]._serialized_start = 9364
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_EXTRACT"]._serialized_end = 9493
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_TABLECOPY"]._serialized_start = 9496
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_TABLECOPY"]._serialized_end = 10008
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_LABELSENTRY"]._serialized_start = 10010
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_LABELSENTRY"]._serialized_end = 10055
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_TYPE"]._serialized_start = 10057
    _globals["_BIGQUERYAUDITMETADATA_JOBCONFIG_TYPE"]._serialized_end = 10130
    _globals["_BIGQUERYAUDITMETADATA_TABLEDEFINITION"]._serialized_start = 10142
    _globals["_BIGQUERYAUDITMETADATA_TABLEDEFINITION"]._serialized_end = 10194
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATUS"]._serialized_start = 10197
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATUS"]._serialized_end = 10357
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS"]._serialized_start = 10360
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS"]._serialized_end = 11298
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS_QUERY"]._serialized_start = 10938
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS_QUERY"]._serialized_end = 11153
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS_LOAD"]._serialized_start = 11155
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS_LOAD"]._serialized_end = 11189
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS_EXTRACT"]._serialized_start = 11191
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATS_EXTRACT"]._serialized_end = 11227
    _globals[
        "_BIGQUERYAUDITMETADATA_JOBSTATS_RESERVATIONRESOURCEUSAGE"
    ]._serialized_start = 11229
    _globals[
        "_BIGQUERYAUDITMETADATA_JOBSTATS_RESERVATIONRESOURCEUSAGE"
    ]._serialized_end = 11286
    _globals["_BIGQUERYAUDITMETADATA_TABLE"]._serialized_start = 11301
    _globals["_BIGQUERYAUDITMETADATA_TABLE"]._serialized_end = 11807
    _globals["_BIGQUERYAUDITMETADATA_MODEL"]._serialized_start = 11810
    _globals["_BIGQUERYAUDITMETADATA_MODEL"]._serialized_end = 12136
    _globals["_BIGQUERYAUDITMETADATA_ROUTINE"]._serialized_start = 12139
    _globals["_BIGQUERYAUDITMETADATA_ROUTINE"]._serialized_end = 12268
    _globals["_BIGQUERYAUDITMETADATA_ENTITYINFO"]._serialized_start = 12271
    _globals["_BIGQUERYAUDITMETADATA_ENTITYINFO"]._serialized_end = 12456
    _globals["_BIGQUERYAUDITMETADATA_ENTITYINFO_LABELSENTRY"]._serialized_start = 10010
    _globals["_BIGQUERYAUDITMETADATA_ENTITYINFO_LABELSENTRY"]._serialized_end = 10055
    _globals["_BIGQUERYAUDITMETADATA_TABLEVIEWDEFINITION"]._serialized_start = 12458
    _globals["_BIGQUERYAUDITMETADATA_TABLEVIEWDEFINITION"]._serialized_end = 12519
    _globals["_BIGQUERYAUDITMETADATA_DATASET"]._serialized_start = 12522
    _globals["_BIGQUERYAUDITMETADATA_DATASET"]._serialized_end = 12974
    _globals["_BIGQUERYAUDITMETADATA_BIGQUERYACL"]._serialized_start = 12976
    _globals["_BIGQUERYAUDITMETADATA_BIGQUERYACL"]._serialized_end = 13054
    _globals["_BIGQUERYAUDITMETADATA_ENCRYPTIONINFO"]._serialized_start = 13056
    _globals["_BIGQUERYAUDITMETADATA_ENCRYPTIONINFO"]._serialized_end = 13094
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICY"]._serialized_start = 13096
    _globals["_BIGQUERYAUDITMETADATA_ROWACCESSPOLICY"]._serialized_end = 13145
    _globals["_BIGQUERYAUDITMETADATA_FIRSTPARTYAPPMETADATA"]._serialized_start = 13147
    _globals["_BIGQUERYAUDITMETADATA_FIRSTPARTYAPPMETADATA"]._serialized_end = 13267
    _globals["_BIGQUERYAUDITMETADATA_SHEETSMETADATA"]._serialized_start = 13269
    _globals["_BIGQUERYAUDITMETADATA_SHEETSMETADATA"]._serialized_end = 13301
    _globals["_BIGQUERYAUDITMETADATA_CREATEDISPOSITION"]._serialized_start = 13303
    _globals["_BIGQUERYAUDITMETADATA_CREATEDISPOSITION"]._serialized_end = 13398
    _globals["_BIGQUERYAUDITMETADATA_WRITEDISPOSITION"]._serialized_start = 13400
    _globals["_BIGQUERYAUDITMETADATA_WRITEDISPOSITION"]._serialized_end = 13508
    _globals["_BIGQUERYAUDITMETADATA_OPERATIONTYPE"]._serialized_start = 13510
    _globals["_BIGQUERYAUDITMETADATA_OPERATIONTYPE"]._serialized_end = 13594
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATE"]._serialized_start = 13596
    _globals["_BIGQUERYAUDITMETADATA_JOBSTATE"]._serialized_end = 13669
    _globals["_BIGQUERYAUDITMETADATA_QUERYSTATEMENTTYPE"]._serialized_start = 13672
    _globals["_BIGQUERYAUDITMETADATA_QUERYSTATEMENTTYPE"]._serialized_end = 14433
# @@protoc_insertion_point(module_scope)
